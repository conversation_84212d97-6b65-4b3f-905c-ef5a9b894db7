<?php
/**
 * Appointment Booking Form
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require admin login
requireLogin('../auth/admin_login.php');
requireRole('admin', '../auth/admin_login.php');

// Get patients and staff for dropdowns
$patients = [];
$doctors = [];

try {
    $patients = fetchAll("SELECT id, full_name, email FROM patient ORDER BY full_name") ?: [];
    $doctors = fetchAll("SELECT id, full_name, specialty FROM staff WHERE role = 'doctor' ORDER BY full_name") ?: [];
} catch (Exception $e) {
    // Handle database errors gracefully
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $patient_id = $_POST['patient_id'] ?? '';
    $staff_id = $_POST['staff_id'] ?? '';
    $appointment_date = $_POST['appointment_date'] ?? '';
    $appointment_time = $_POST['appointment_time'] ?? '';
    $appointment_type = $_POST['appointment_type'] ?? '';
    $reason = $_POST['reason'] ?? '';
    $notes = $_POST['notes'] ?? '';
    $status = $_POST['status'] ?? 'scheduled';
    
    // Validation
    $errors = [];
    if (empty($patient_id)) $errors[] = "Patient is required";
    if (empty($staff_id)) $errors[] = "Doctor is required";
    if (empty($appointment_date)) $errors[] = "Appointment date is required";
    if (empty($appointment_time)) $errors[] = "Appointment time is required";
    if (empty($appointment_type)) $errors[] = "Appointment type is required";
    if (empty($reason)) $errors[] = "Reason for visit is required";
    
    // Check if appointment date is not in the past
    if (!empty($appointment_date) && $appointment_date < date('Y-m-d')) {
        $errors[] = "Appointment date cannot be in the past";
    }
    
    if (empty($errors)) {
        try {
            // Check for conflicting appointments
            $conflict_check = fetchOne(
                "SELECT id FROM appointment WHERE staff_id = ? AND appointment_date = ? AND appointment_time = ? AND status != 'cancelled'",
                "iss",
                [$staff_id, $appointment_date, $appointment_time]
            );
            
            if ($conflict_check) {
                $errors[] = "The selected doctor already has an appointment at this time";
            } else {
                // Insert appointment record
                $sql = "INSERT INTO appointment (patient_id, staff_id, appointment_date, appointment_time, appointment_type, reason, notes, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                $result = executeQuery($sql, "iissssss", [
                    $patient_id, $staff_id, $appointment_date, $appointment_time,
                    $appointment_type, $reason, $notes, $status
                ]);
                
                if ($result) {
                    $_SESSION['flash_message'] = "Appointment booked successfully!";
                    $_SESSION['flash_type'] = "success";
                    header("Location: appointment_booking.php");
                    exit;
                } else {
                    $errors[] = "Failed to book appointment. Please try again.";
                }
            }
        } catch (Exception $e) {
            $errors[] = "Database error: " . $e->getMessage();
        }
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl shadow-lg p-8 mb-8">
        <div class="flex items-center">
            <i class="fas fa-calendar-plus text-4xl mr-4"></i>
            <div>
                <h1 class="text-3xl font-bold">Appointment Booking</h1>
                <p class="text-green-100 mt-1">Schedule new appointments for patients</p>
            </div>
        </div>
    </div>

    <!-- Display Errors -->
    <?php if (!empty($errors)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <ul class="list-disc list-inside">
                <?php foreach ($errors as $error): ?>
                    <li><?= htmlspecialchars($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Booking Form -->
    <div class="bg-white rounded-xl shadow-lg p-8">
        <form method="POST" class="space-y-6">
            <!-- Patient and Doctor Selection -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-users text-blue-600 mr-2"></i>
                    Patient & Doctor Selection
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Patient *</label>
                        <select name="patient_id" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Patient</option>
                            <?php foreach ($patients as $patient): ?>
                                <option value="<?= $patient['id'] ?>" <?= ($_POST['patient_id'] ?? '') == $patient['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($patient['full_name']) ?> (<?= htmlspecialchars($patient['email']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Doctor *</label>
                        <select name="staff_id" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Doctor</option>
                            <?php foreach ($doctors as $doctor): ?>
                                <option value="<?= $doctor['id'] ?>" <?= ($_POST['staff_id'] ?? '') == $doctor['id'] ? 'selected' : '' ?>>
                                    Dr. <?= htmlspecialchars($doctor['full_name']) ?> 
                                    <?= $doctor['specialty'] ? '(' . htmlspecialchars($doctor['specialty']) . ')' : '' ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Appointment Details -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-calendar text-green-600 mr-2"></i>
                    Appointment Details
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Appointment Date *</label>
                        <input type="date" name="appointment_date" required 
                               class="form-input border-gray-300 focus:border-blue-500"
                               min="<?= date('Y-m-d') ?>"
                               value="<?= htmlspecialchars($_POST['appointment_date'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Appointment Time *</label>
                        <select name="appointment_time" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Time</option>
                            <?php
                            $times = [
                                '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
                                '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30',
                                '16:00', '16:30', '17:00', '17:30'
                            ];
                            foreach ($times as $time): ?>
                                <option value="<?= $time ?>" <?= ($_POST['appointment_time'] ?? '') === $time ? 'selected' : '' ?>>
                                    <?= date('g:i A', strtotime($time)) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Appointment Type *</label>
                        <select name="appointment_type" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Type</option>
                            <option value="consultation" <?= ($_POST['appointment_type'] ?? '') === 'consultation' ? 'selected' : '' ?>>Consultation</option>
                            <option value="follow-up" <?= ($_POST['appointment_type'] ?? '') === 'follow-up' ? 'selected' : '' ?>>Follow-up</option>
                            <option value="emergency" <?= ($_POST['appointment_type'] ?? '') === 'emergency' ? 'selected' : '' ?>>Emergency</option>
                            <option value="surgery" <?= ($_POST['appointment_type'] ?? '') === 'surgery' ? 'selected' : '' ?>>Surgery</option>
                            <option value="diagnostic" <?= ($_POST['appointment_type'] ?? '') === 'diagnostic' ? 'selected' : '' ?>>Diagnostic</option>
                            <option value="therapy" <?= ($_POST['appointment_type'] ?? '') === 'therapy' ? 'selected' : '' ?>>Therapy</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Status</label>
                        <select name="status" class="form-input border-gray-300 focus:border-blue-500">
                            <option value="scheduled" <?= ($_POST['status'] ?? 'scheduled') === 'scheduled' ? 'selected' : '' ?>>Scheduled</option>
                            <option value="confirmed" <?= ($_POST['status'] ?? '') === 'confirmed' ? 'selected' : '' ?>>Confirmed</option>
                            <option value="pending" <?= ($_POST['status'] ?? '') === 'pending' ? 'selected' : '' ?>>Pending</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Reason and Notes -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-notes-medical text-purple-600 mr-2"></i>
                    Additional Information
                </h3>
                <div class="space-y-6">
                    <div>
                        <label class="form-label text-gray-700">Reason for Visit *</label>
                        <textarea name="reason" rows="3" required
                                  class="form-input border-gray-300 focus:border-blue-500" 
                                  placeholder="Enter the reason for this appointment"><?= htmlspecialchars($_POST['reason'] ?? '') ?></textarea>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Additional Notes</label>
                        <textarea name="notes" rows="4" 
                                  class="form-input border-gray-300 focus:border-blue-500" 
                                  placeholder="Any additional notes or special instructions"><?= htmlspecialchars($_POST['notes'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="window.history.back()" 
                        class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
                    Cancel
                </button>
                <button type="submit" 
                        class="btn-primary px-8 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition">
                    <i class="fas fa-calendar-check mr-2"></i>
                    Book Appointment
                </button>
            </div>
        </form>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
