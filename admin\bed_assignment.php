<?php
/**
 * Bed Assignment Form
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require admin login
requireLogin('../auth/admin_login.php');
requireRole('admin', '../auth/admin_login.php');

// Get patients for dropdown
$patients = [];
try {
    $patients = fetchAll("SELECT id, full_name, email FROM patient ORDER BY full_name") ?: [];
} catch (Exception $e) {
    // Handle database errors gracefully
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $patient_id = $_POST['patient_id'] ?? '';
    $room_number = $_POST['room_number'] ?? '';
    $bed_number = $_POST['bed_number'] ?? '';
    $ward = $_POST['ward'] ?? '';
    $bed_type = $_POST['bed_type'] ?? '';
    $admission_date = $_POST['admission_date'] ?? '';
    $expected_discharge = $_POST['expected_discharge'] ?? '';
    $admission_reason = $_POST['admission_reason'] ?? '';
    $notes = $_POST['notes'] ?? '';
    $status = $_POST['status'] ?? 'occupied';
    
    // Validation
    $errors = [];
    if (empty($patient_id)) $errors[] = "Patient is required";
    if (empty($room_number)) $errors[] = "Room number is required";
    if (empty($bed_number)) $errors[] = "Bed number is required";
    if (empty($ward)) $errors[] = "Ward is required";
    if (empty($bed_type)) $errors[] = "Bed type is required";
    if (empty($admission_date)) $errors[] = "Admission date is required";
    if (empty($admission_reason)) $errors[] = "Admission reason is required";
    
    if (empty($errors)) {
        try {
            // Check if bed is already occupied
            $bed_check = fetchOne(
                "SELECT id FROM bed_assignment WHERE room_number = ? AND bed_number = ? AND status = 'occupied'",
                "ss",
                [$room_number, $bed_number]
            );
            
            if ($bed_check) {
                $errors[] = "This bed is already occupied";
            } else {
                // Insert bed assignment record
                $sql = "INSERT INTO bed_assignment (patient_id, room_number, bed_number, ward, bed_type, admission_date, expected_discharge, admission_reason, notes, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                $result = executeQuery($sql, "isssssssss", [
                    $patient_id, $room_number, $bed_number, $ward, $bed_type,
                    $admission_date, $expected_discharge, $admission_reason, $notes, $status
                ]);
                
                if ($result) {
                    $_SESSION['flash_message'] = "Bed assigned successfully!";
                    $_SESSION['flash_type'] = "success";
                    header("Location: bed_assignment.php");
                    exit;
                } else {
                    $errors[] = "Failed to assign bed. Please try again.";
                }
            }
        } catch (Exception $e) {
            $errors[] = "Database error: " . $e->getMessage();
        }
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-teal-600 to-cyan-600 text-white rounded-xl shadow-lg p-8 mb-8">
        <div class="flex items-center">
            <i class="fas fa-bed text-4xl mr-4"></i>
            <div>
                <h1 class="text-3xl font-bold">Bed Assignment</h1>
                <p class="text-teal-100 mt-1">Assign beds to patients for admission</p>
            </div>
        </div>
    </div>

    <!-- Display Errors -->
    <?php if (!empty($errors)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <ul class="list-disc list-inside">
                <?php foreach ($errors as $error): ?>
                    <li><?= htmlspecialchars($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Assignment Form -->
    <div class="bg-white rounded-xl shadow-lg p-8">
        <form method="POST" class="space-y-6">
            <!-- Patient Selection -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-user text-blue-600 mr-2"></i>
                    Patient Information
                </h3>
                <div>
                    <label class="form-label text-gray-700">Patient *</label>
                    <select name="patient_id" required class="form-input border-gray-300 focus:border-blue-500">
                        <option value="">Select Patient</option>
                        <?php foreach ($patients as $patient): ?>
                            <option value="<?= $patient['id'] ?>" <?= ($_POST['patient_id'] ?? '') == $patient['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($patient['full_name']) ?> (<?= htmlspecialchars($patient['email']) ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <!-- Bed Details -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-hospital text-green-600 mr-2"></i>
                    Bed Details
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Ward *</label>
                        <select name="ward" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Ward</option>
                            <option value="general" <?= ($_POST['ward'] ?? '') === 'general' ? 'selected' : '' ?>>General Ward</option>
                            <option value="icu" <?= ($_POST['ward'] ?? '') === 'icu' ? 'selected' : '' ?>>ICU</option>
                            <option value="emergency" <?= ($_POST['ward'] ?? '') === 'emergency' ? 'selected' : '' ?>>Emergency Ward</option>
                            <option value="pediatric" <?= ($_POST['ward'] ?? '') === 'pediatric' ? 'selected' : '' ?>>Pediatric Ward</option>
                            <option value="maternity" <?= ($_POST['ward'] ?? '') === 'maternity' ? 'selected' : '' ?>>Maternity Ward</option>
                            <option value="surgical" <?= ($_POST['ward'] ?? '') === 'surgical' ? 'selected' : '' ?>>Surgical Ward</option>
                            <option value="cardiac" <?= ($_POST['ward'] ?? '') === 'cardiac' ? 'selected' : '' ?>>Cardiac Ward</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Bed Type *</label>
                        <select name="bed_type" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Bed Type</option>
                            <option value="standard" <?= ($_POST['bed_type'] ?? '') === 'standard' ? 'selected' : '' ?>>Standard Bed</option>
                            <option value="private" <?= ($_POST['bed_type'] ?? '') === 'private' ? 'selected' : '' ?>>Private Room</option>
                            <option value="icu" <?= ($_POST['bed_type'] ?? '') === 'icu' ? 'selected' : '' ?>>ICU Bed</option>
                            <option value="isolation" <?= ($_POST['bed_type'] ?? '') === 'isolation' ? 'selected' : '' ?>>Isolation Bed</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Room Number *</label>
                        <input type="text" name="room_number" required 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="e.g., 101, A-205"
                               value="<?= htmlspecialchars($_POST['room_number'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Bed Number *</label>
                        <input type="text" name="bed_number" required 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="e.g., 1, A, B"
                               value="<?= htmlspecialchars($_POST['bed_number'] ?? '') ?>">
                    </div>
                </div>
            </div>

            <!-- Admission Details -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-calendar text-purple-600 mr-2"></i>
                    Admission Details
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Admission Date *</label>
                        <input type="date" name="admission_date" required 
                               class="form-input border-gray-300 focus:border-blue-500"
                               value="<?= htmlspecialchars($_POST['admission_date'] ?? date('Y-m-d')) ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Expected Discharge Date</label>
                        <input type="date" name="expected_discharge" 
                               class="form-input border-gray-300 focus:border-blue-500"
                               value="<?= htmlspecialchars($_POST['expected_discharge'] ?? '') ?>">
                    </div>
                    <div class="md:col-span-2">
                        <label class="form-label text-gray-700">Admission Reason *</label>
                        <textarea name="admission_reason" rows="3" required
                                  class="form-input border-gray-300 focus:border-blue-500" 
                                  placeholder="Enter reason for admission"><?= htmlspecialchars($_POST['admission_reason'] ?? '') ?></textarea>
                    </div>
                    <div class="md:col-span-2">
                        <label class="form-label text-gray-700">Additional Notes</label>
                        <textarea name="notes" rows="3" 
                                  class="form-input border-gray-300 focus:border-blue-500" 
                                  placeholder="Any additional notes or special requirements"><?= htmlspecialchars($_POST['notes'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="window.history.back()" 
                        class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
                    Cancel
                </button>
                <button type="submit" 
                        class="btn-primary px-8 py-3 bg-teal-600 hover:bg-teal-700 text-white rounded-lg transition">
                    <i class="fas fa-bed mr-2"></i>
                    Assign Bed
                </button>
            </div>
        </form>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
