<?php
/**
 * Staff Registration Form
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require admin login
requireLogin('../auth/admin_login.php');
requireRole('admin', '../auth/admin_login.php');

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = $_POST['full_name'] ?? '';
    $email = $_POST['email'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $role = $_POST['role'] ?? '';
    $specialty = $_POST['specialty'] ?? '';
    $department = $_POST['department'] ?? '';
    $license_number = $_POST['license_number'] ?? '';
    $hire_date = $_POST['hire_date'] ?? '';
    $salary = $_POST['salary'] ?? '';
    $address = $_POST['address'] ?? '';
    $emergency_contact = $_POST['emergency_contact'] ?? '';
    $emergency_phone = $_POST['emergency_phone'] ?? '';
    
    // Validation
    $errors = [];
    if (empty($full_name)) $errors[] = "Full name is required";
    if (empty($email)) $errors[] = "Email is required";
    if (empty($phone)) $errors[] = "Phone number is required";
    if (empty($role)) $errors[] = "Role is required";
    if (empty($department)) $errors[] = "Department is required";
    if (empty($hire_date)) $errors[] = "Hire date is required";
    
    if (empty($errors)) {
        try {
            // Insert staff record
            $sql = "INSERT INTO staff (full_name, email, phone, role, specialty, department, license_number, hire_date, salary, address, emergency_contact, emergency_phone, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            $result = executeQuery($sql, "ssssssssdsss", [
                $full_name, $email, $phone, $role, $specialty, $department,
                $license_number, $hire_date, $salary, $address, $emergency_contact, $emergency_phone
            ]);
            
            if ($result) {
                $_SESSION['flash_message'] = "Staff member registered successfully!";
                $_SESSION['flash_type'] = "success";
                header("Location: staff_registration.php");
                exit;
            } else {
                $errors[] = "Failed to register staff member. Please try again.";
            }
        } catch (Exception $e) {
            $errors[] = "Database error: " . $e->getMessage();
        }
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl shadow-lg p-8 mb-8">
        <div class="flex items-center">
            <i class="fas fa-user-md text-4xl mr-4"></i>
            <div>
                <h1 class="text-3xl font-bold">Staff Registration</h1>
                <p class="text-purple-100 mt-1">Register new staff members into the hospital system</p>
            </div>
        </div>
    </div>

    <!-- Display Errors -->
    <?php if (!empty($errors)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <ul class="list-disc list-inside">
                <?php foreach ($errors as $error): ?>
                    <li><?= htmlspecialchars($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Registration Form -->
    <div class="bg-white rounded-xl shadow-lg p-8">
        <form method="POST" class="space-y-6">
            <!-- Personal Information Section -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-user text-blue-600 mr-2"></i>
                    Personal Information
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Full Name *</label>
                        <input type="text" name="full_name" required 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Enter staff member's full name"
                               value="<?= htmlspecialchars($_POST['full_name'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Email Address *</label>
                        <input type="email" name="email" required 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="<EMAIL>"
                               value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Phone Number *</label>
                        <input type="tel" name="phone" required 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="+234 xxx xxx xxxx"
                               value="<?= htmlspecialchars($_POST['phone'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Hire Date *</label>
                        <input type="date" name="hire_date" required 
                               class="form-input border-gray-300 focus:border-blue-500"
                               value="<?= htmlspecialchars($_POST['hire_date'] ?? '') ?>">
                    </div>
                </div>
                <div class="mt-6">
                    <label class="form-label text-gray-700">Address</label>
                    <textarea name="address" rows="3" 
                              class="form-input border-gray-300 focus:border-blue-500" 
                              placeholder="Enter staff member's address"><?= htmlspecialchars($_POST['address'] ?? '') ?></textarea>
                </div>
            </div>

            <!-- Professional Information Section -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-briefcase text-green-600 mr-2"></i>
                    Professional Information
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Role *</label>
                        <select name="role" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Role</option>
                            <option value="doctor" <?= ($_POST['role'] ?? '') === 'doctor' ? 'selected' : '' ?>>Doctor</option>
                            <option value="nurse" <?= ($_POST['role'] ?? '') === 'nurse' ? 'selected' : '' ?>>Nurse</option>
                            <option value="technician" <?= ($_POST['role'] ?? '') === 'technician' ? 'selected' : '' ?>>Technician</option>
                            <option value="pharmacist" <?= ($_POST['role'] ?? '') === 'pharmacist' ? 'selected' : '' ?>>Pharmacist</option>
                            <option value="administrator" <?= ($_POST['role'] ?? '') === 'administrator' ? 'selected' : '' ?>>Administrator</option>
                            <option value="receptionist" <?= ($_POST['role'] ?? '') === 'receptionist' ? 'selected' : '' ?>>Receptionist</option>
                            <option value="security" <?= ($_POST['role'] ?? '') === 'security' ? 'selected' : '' ?>>Security</option>
                            <option value="maintenance" <?= ($_POST['role'] ?? '') === 'maintenance' ? 'selected' : '' ?>>Maintenance</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Department *</label>
                        <select name="department" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Department</option>
                            <option value="emergency" <?= ($_POST['department'] ?? '') === 'emergency' ? 'selected' : '' ?>>Emergency</option>
                            <option value="cardiology" <?= ($_POST['department'] ?? '') === 'cardiology' ? 'selected' : '' ?>>Cardiology</option>
                            <option value="neurology" <?= ($_POST['department'] ?? '') === 'neurology' ? 'selected' : '' ?>>Neurology</option>
                            <option value="orthopedics" <?= ($_POST['department'] ?? '') === 'orthopedics' ? 'selected' : '' ?>>Orthopedics</option>
                            <option value="pediatrics" <?= ($_POST['department'] ?? '') === 'pediatrics' ? 'selected' : '' ?>>Pediatrics</option>
                            <option value="surgery" <?= ($_POST['department'] ?? '') === 'surgery' ? 'selected' : '' ?>>Surgery</option>
                            <option value="radiology" <?= ($_POST['department'] ?? '') === 'radiology' ? 'selected' : '' ?>>Radiology</option>
                            <option value="laboratory" <?= ($_POST['department'] ?? '') === 'laboratory' ? 'selected' : '' ?>>Laboratory</option>
                            <option value="pharmacy" <?= ($_POST['department'] ?? '') === 'pharmacy' ? 'selected' : '' ?>>Pharmacy</option>
                            <option value="administration" <?= ($_POST['department'] ?? '') === 'administration' ? 'selected' : '' ?>>Administration</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Specialty</label>
                        <input type="text" name="specialty" 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="e.g., Cardiologist, Pediatric Nurse"
                               value="<?= htmlspecialchars($_POST['specialty'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">License Number</label>
                        <input type="text" name="license_number" 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Professional license number"
                               value="<?= htmlspecialchars($_POST['license_number'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Salary</label>
                        <input type="number" name="salary" step="0.01" 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Monthly salary"
                               value="<?= htmlspecialchars($_POST['salary'] ?? '') ?>">
                    </div>
                </div>
            </div>

            <!-- Emergency Contact Section -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-phone text-red-600 mr-2"></i>
                    Emergency Contact
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Emergency Contact Name</label>
                        <input type="text" name="emergency_contact" 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Emergency contact full name"
                               value="<?= htmlspecialchars($_POST['emergency_contact'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Emergency Contact Phone</label>
                        <input type="tel" name="emergency_phone" 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="+234 xxx xxx xxxx"
                               value="<?= htmlspecialchars($_POST['emergency_phone'] ?? '') ?>">
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="window.history.back()" 
                        class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
                    Cancel
                </button>
                <button type="submit" 
                        class="btn-primary px-8 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition">
                    <i class="fas fa-save mr-2"></i>
                    Register Staff Member
                </button>
            </div>
        </form>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
