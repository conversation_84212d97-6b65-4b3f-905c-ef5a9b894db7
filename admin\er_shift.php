<?php
/**
 * ER Shift Management
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require admin login
requireLogin('../auth/admin_login.php');
requireRole('admin', '../auth/admin_login.php');

// Get staff for dropdown
$staff = [];
try {
    $staff = fetchAll("SELECT id, full_name, role, specialty FROM staff WHERE role IN ('doctor', 'nurse') ORDER BY role, full_name") ?: [];
} catch (Exception $e) {
    // Handle database errors gracefully
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $staff_id = $_POST['staff_id'] ?? '';
    $shift_date = $_POST['shift_date'] ?? '';
    $shift_type = $_POST['shift_type'] ?? '';
    $start_time = $_POST['start_time'] ?? '';
    $end_time = $_POST['end_time'] ?? '';
    $role_in_shift = $_POST['role_in_shift'] ?? '';
    $notes = $_POST['notes'] ?? '';
    $status = $_POST['status'] ?? 'scheduled';
    
    // Validation
    $errors = [];
    if (empty($staff_id)) $errors[] = "Staff member is required";
    if (empty($shift_date)) $errors[] = "Shift date is required";
    if (empty($shift_type)) $errors[] = "Shift type is required";
    if (empty($start_time)) $errors[] = "Start time is required";
    if (empty($end_time)) $errors[] = "End time is required";
    if (empty($role_in_shift)) $errors[] = "Role in shift is required";
    
    // Check if end time is after start time
    if (!empty($start_time) && !empty($end_time) && $end_time <= $start_time) {
        $errors[] = "End time must be after start time";
    }
    
    if (empty($errors)) {
        try {
            // Check for overlapping shifts
            $overlap_check = fetchOne(
                "SELECT id FROM er_shift WHERE staff_id = ? AND shift_date = ? AND status != 'cancelled' AND ((start_time <= ? AND end_time > ?) OR (start_time < ? AND end_time >= ?))",
                "isssss",
                [$staff_id, $shift_date, $start_time, $start_time, $end_time, $end_time]
            );
            
            if ($overlap_check) {
                $errors[] = "This staff member already has an overlapping shift on this date";
            } else {
                // Insert ER shift record
                $sql = "INSERT INTO er_shift (staff_id, shift_date, shift_type, start_time, end_time, role_in_shift, notes, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                $result = executeQuery($sql, "isssssss", [
                    $staff_id, $shift_date, $shift_type, $start_time, $end_time, $role_in_shift, $notes, $status
                ]);
                
                if ($result) {
                    $_SESSION['flash_message'] = "ER shift scheduled successfully!";
                    $_SESSION['flash_type'] = "success";
                    header("Location: er_shift.php");
                    exit;
                } else {
                    $errors[] = "Failed to schedule ER shift. Please try again.";
                }
            }
        } catch (Exception $e) {
            $errors[] = "Database error: " . $e->getMessage();
        }
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-yellow-600 to-red-600 text-white rounded-xl shadow-lg p-8 mb-8">
        <div class="flex items-center">
            <i class="fas fa-clock text-4xl mr-4"></i>
            <div>
                <h1 class="text-3xl font-bold">ER Shift Management</h1>
                <p class="text-yellow-100 mt-1">Schedule and manage emergency room shifts</p>
            </div>
        </div>
    </div>

    <!-- Display Errors -->
    <?php if (!empty($errors)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <ul class="list-disc list-inside">
                <?php foreach ($errors as $error): ?>
                    <li><?= htmlspecialchars($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Shift Form -->
    <div class="bg-white rounded-xl shadow-lg p-8">
        <form method="POST" class="space-y-6">
            <!-- Staff Selection -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-user-md text-blue-600 mr-2"></i>
                    Staff Information
                </h3>
                <div>
                    <label class="form-label text-gray-700">Staff Member *</label>
                    <select name="staff_id" required class="form-input border-gray-300 focus:border-blue-500">
                        <option value="">Select Staff Member</option>
                        <?php 
                        $current_role = '';
                        foreach ($staff as $member): 
                            if ($current_role !== $member['role']):
                                if ($current_role !== '') echo '</optgroup>';
                                echo '<optgroup label="' . ucfirst($member['role']) . 's">';
                                $current_role = $member['role'];
                            endif;
                        ?>
                            <option value="<?= $member['id'] ?>" <?= ($_POST['staff_id'] ?? '') == $member['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($member['full_name']) ?>
                                <?= $member['specialty'] ? ' (' . htmlspecialchars($member['specialty']) . ')' : '' ?>
                            </option>
                        <?php 
                        endforeach; 
                        if ($current_role !== '') echo '</optgroup>';
                        ?>
                    </select>
                </div>
            </div>

            <!-- Shift Details -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-calendar text-green-600 mr-2"></i>
                    Shift Details
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Shift Date *</label>
                        <input type="date" name="shift_date" required 
                               class="form-input border-gray-300 focus:border-blue-500"
                               min="<?= date('Y-m-d') ?>"
                               value="<?= htmlspecialchars($_POST['shift_date'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Shift Type *</label>
                        <select name="shift_type" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Shift Type</option>
                            <option value="day" <?= ($_POST['shift_type'] ?? '') === 'day' ? 'selected' : '' ?>>Day Shift</option>
                            <option value="evening" <?= ($_POST['shift_type'] ?? '') === 'evening' ? 'selected' : '' ?>>Evening Shift</option>
                            <option value="night" <?= ($_POST['shift_type'] ?? '') === 'night' ? 'selected' : '' ?>>Night Shift</option>
                            <option value="on_call" <?= ($_POST['shift_type'] ?? '') === 'on_call' ? 'selected' : '' ?>>On-Call</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Start Time *</label>
                        <input type="time" name="start_time" required 
                               class="form-input border-gray-300 focus:border-blue-500"
                               value="<?= htmlspecialchars($_POST['start_time'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">End Time *</label>
                        <input type="time" name="end_time" required 
                               class="form-input border-gray-300 focus:border-blue-500"
                               value="<?= htmlspecialchars($_POST['end_time'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Role in Shift *</label>
                        <select name="role_in_shift" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Role</option>
                            <option value="attending_physician" <?= ($_POST['role_in_shift'] ?? '') === 'attending_physician' ? 'selected' : '' ?>>Attending Physician</option>
                            <option value="resident" <?= ($_POST['role_in_shift'] ?? '') === 'resident' ? 'selected' : '' ?>>Resident</option>
                            <option value="charge_nurse" <?= ($_POST['role_in_shift'] ?? '') === 'charge_nurse' ? 'selected' : '' ?>>Charge Nurse</option>
                            <option value="staff_nurse" <?= ($_POST['role_in_shift'] ?? '') === 'staff_nurse' ? 'selected' : '' ?>>Staff Nurse</option>
                            <option value="triage_nurse" <?= ($_POST['role_in_shift'] ?? '') === 'triage_nurse' ? 'selected' : '' ?>>Triage Nurse</option>
                            <option value="technician" <?= ($_POST['role_in_shift'] ?? '') === 'technician' ? 'selected' : '' ?>>Technician</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Status</label>
                        <select name="status" class="form-input border-gray-300 focus:border-blue-500">
                            <option value="scheduled" <?= ($_POST['status'] ?? 'scheduled') === 'scheduled' ? 'selected' : '' ?>>Scheduled</option>
                            <option value="confirmed" <?= ($_POST['status'] ?? '') === 'confirmed' ? 'selected' : '' ?>>Confirmed</option>
                            <option value="in_progress" <?= ($_POST['status'] ?? '') === 'in_progress' ? 'selected' : '' ?>>In Progress</option>
                            <option value="completed" <?= ($_POST['status'] ?? '') === 'completed' ? 'selected' : '' ?>>Completed</option>
                            <option value="cancelled" <?= ($_POST['status'] ?? '') === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-sticky-note text-purple-600 mr-2"></i>
                    Additional Information
                </h3>
                <div>
                    <label class="form-label text-gray-700">Notes</label>
                    <textarea name="notes" rows="4" 
                              class="form-input border-gray-300 focus:border-blue-500" 
                              placeholder="Any special instructions, coverage arrangements, or additional notes"><?= htmlspecialchars($_POST['notes'] ?? '') ?></textarea>
                </div>
            </div>

            <!-- Quick Shift Templates -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Shift Templates</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button type="button" onclick="setShiftTemplate('day')" 
                            class="p-4 bg-blue-100 hover:bg-blue-200 rounded-lg text-left transition">
                        <div class="font-medium text-blue-900">Day Shift</div>
                        <div class="text-sm text-blue-700">7:00 AM - 7:00 PM</div>
                    </button>
                    <button type="button" onclick="setShiftTemplate('evening')" 
                            class="p-4 bg-orange-100 hover:bg-orange-200 rounded-lg text-left transition">
                        <div class="font-medium text-orange-900">Evening Shift</div>
                        <div class="text-sm text-orange-700">3:00 PM - 11:00 PM</div>
                    </button>
                    <button type="button" onclick="setShiftTemplate('night')" 
                            class="p-4 bg-purple-100 hover:bg-purple-200 rounded-lg text-left transition">
                        <div class="font-medium text-purple-900">Night Shift</div>
                        <div class="text-sm text-purple-700">11:00 PM - 7:00 AM</div>
                    </button>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="window.history.back()" 
                        class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
                    Cancel
                </button>
                <button type="submit" 
                        class="btn-primary px-8 py-3 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition">
                    <i class="fas fa-save mr-2"></i>
                    Schedule Shift
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function setShiftTemplate(type) {
    const shiftTypeSelect = document.querySelector('select[name="shift_type"]');
    const startTimeInput = document.querySelector('input[name="start_time"]');
    const endTimeInput = document.querySelector('input[name="end_time"]');
    
    shiftTypeSelect.value = type;
    
    switch(type) {
        case 'day':
            startTimeInput.value = '07:00';
            endTimeInput.value = '19:00';
            break;
        case 'evening':
            startTimeInput.value = '15:00';
            endTimeInput.value = '23:00';
            break;
        case 'night':
            startTimeInput.value = '23:00';
            endTimeInput.value = '07:00';
            break;
    }
}
</script>

<?php include_once '../includes/footer.php'; ?>
