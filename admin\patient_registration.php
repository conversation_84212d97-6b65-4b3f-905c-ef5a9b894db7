<?php
/**
 * Patient Registration Form
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require admin login
requireLogin('../auth/admin_login.php');
requireRole('admin', '../auth/admin_login.php');

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = $_POST['full_name'] ?? '';
    $email = $_POST['email'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $date_of_birth = $_POST['date_of_birth'] ?? '';
    $gender = $_POST['gender'] ?? '';
    $address = $_POST['address'] ?? '';
    $emergency_contact = $_POST['emergency_contact'] ?? '';
    $emergency_phone = $_POST['emergency_phone'] ?? '';
    $medical_history = $_POST['medical_history'] ?? '';
    $allergies = $_POST['allergies'] ?? '';
    $blood_type = $_POST['blood_type'] ?? '';

    // Validation
    $errors = [];
    if (empty($full_name)) $errors[] = "Full name is required";
    if (empty($email)) $errors[] = "Email is required";
    if (empty($phone)) $errors[] = "Phone number is required";
    if (empty($date_of_birth)) $errors[] = "Date of birth is required";
    if (empty($gender)) $errors[] = "Gender is required";

    if (empty($errors)) {
        try {
            // Insert patient record
            $sql = "INSERT INTO patient (full_name, email, phone, date_of_birth, gender, address, emergency_contact, emergency_phone, medical_history, allergies, blood_type, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            $result = executeQuery($sql, "sssssssssss", [
                $full_name, $email, $phone, $date_of_birth, $gender,
                $address, $emergency_contact, $emergency_phone,
                $medical_history, $allergies, $blood_type
            ]);

            if ($result) {
                $_SESSION['flash_message'] = "Patient registered successfully!";
                $_SESSION['flash_type'] = "success";
                header("Location: patient_registration.php");
                exit;
            } else {
                $errors[] = "Failed to register patient. Please try again.";
            }
        } catch (Exception $e) {
            $errors[] = "Database error: " . $e->getMessage();
        }
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-xl shadow-lg p-8 mb-8">
        <div class="flex items-center">
            <i class="fas fa-user-plus text-4xl mr-4"></i>
            <div>
                <h1 class="text-3xl font-bold">Patient Registration</h1>
                <p class="text-blue-100 mt-1">Register new patients into the hospital system</p>
            </div>
        </div>
    </div>

    <!-- Display Errors -->
    <?php if (!empty($errors)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <ul class="list-disc list-inside">
                <?php foreach ($errors as $error): ?>
                    <li><?= htmlspecialchars($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Registration Form -->
    <div class="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
        <h1 class="text-2xl font-medium text-gray-800 dark:text-white mb-8 pb-4 border-b border-gray-200 dark:border-gray-600">Patient Registration Form</h1>

        <form method="POST" class="space-y-6" id="patientForm">
            <!-- Name Section -->
            <div class="space-y-4">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Name</label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <input type="text" name="first_name" required
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                           placeholder="First"
                           value="<?= htmlspecialchars($_POST['first_name'] ?? '') ?>">
                    <input type="text" name="surname" required
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                           placeholder="Last"
                           value="<?= htmlspecialchars($_POST['surname'] ?? '') ?>">
                </div>
                <div class="mt-2">
                    <input type="text" name="middle_name"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                           placeholder="Middle Name (Optional)"
                           value="<?= htmlspecialchars($_POST['middle_name'] ?? '') ?>">
                </div>
            </div>

            <!-- Date and Email Section -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date</label>
                    <input type="date" name="date_of_birth" required
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                           value="<?= htmlspecialchars($_POST['date_of_birth'] ?? '') ?>">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Patient e-mail</label>
                    <input type="email" name="email"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                           placeholder="<EMAIL>"
                           value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                </div>
            </div>

            <!-- Patient ID and Gender -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Patient ID</label>
                    <input type="number" name="age"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                           placeholder="Age (Auto-calculated)"
                           value="<?= htmlspecialchars($_POST['age'] ?? '') ?>" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Gender</label>
                    <select name="gender" required
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="">Select Gender</option>
                        <option value="Male" <?= ($_POST['gender'] ?? '') === 'Male' ? 'selected' : '' ?>>Male</option>
                        <option value="Female" <?= ($_POST['gender'] ?? '') === 'Female' ? 'selected' : '' ?>>Female</option>
                        <option value="Other" <?= ($_POST['gender'] ?? '') === 'Other' ? 'selected' : '' ?>>Other</option>
                    </select>
                </div>
            </div>

            <!-- Contact Number -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Contact Number</label>
                <input type="tel" name="contact_number"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                       placeholder="+234 xxx xxx xxxx"
                       value="<?= htmlspecialchars($_POST['contact_number'] ?? '') ?>">
            </div>

            <!-- Emergency Contact -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Emergency Contact</label>
                <input type="text" name="emergency_contact"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                       placeholder="Emergency contact name"
                       value="<?= htmlspecialchars($_POST['emergency_contact'] ?? '') ?>">
            </div>

            <!-- Admission Date -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Admission Date</label>
                <input type="date" name="admitted_date"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                       value="<?= htmlspecialchars($_POST['admitted_date'] ?? '') ?>">
            </div>

            <!-- Address -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address</label>
                <textarea name="address" rows="4"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                          placeholder="Enter complete address"><?= htmlspecialchars($_POST['address'] ?? '') ?></textarea>
            </div>

            <!-- Submit Button -->
            <div class="pt-6">
                <button type="submit"
                        class="bg-gray-800 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-500 text-white px-6 py-2 rounded-md transition-colors">
                    Submit
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Auto-calculate age from date of birth
document.addEventListener('DOMContentLoaded', function() {
    const dobInput = document.querySelector('input[name="date_of_birth"]');
    const ageInput = document.querySelector('input[name="age"]');

    if (dobInput && ageInput) {
        dobInput.addEventListener('change', function() {
            const birthDate = new Date(this.value);
            const today = new Date();
            let age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();

            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }

            ageInput.value = age >= 0 ? age : '';
        });
    }
});
</script>

<?php include_once '../includes/footer.php'; ?>
