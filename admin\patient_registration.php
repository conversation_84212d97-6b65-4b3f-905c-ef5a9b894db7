<?php
/**
 * Patient Registration Form
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require admin login
requireLogin('../auth/admin_login.php');
requireRole('admin', '../auth/admin_login.php');

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = $_POST['full_name'] ?? '';
    $email = $_POST['email'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $date_of_birth = $_POST['date_of_birth'] ?? '';
    $gender = $_POST['gender'] ?? '';
    $address = $_POST['address'] ?? '';
    $emergency_contact = $_POST['emergency_contact'] ?? '';
    $emergency_phone = $_POST['emergency_phone'] ?? '';
    $medical_history = $_POST['medical_history'] ?? '';
    $allergies = $_POST['allergies'] ?? '';
    $blood_type = $_POST['blood_type'] ?? '';
    
    // Validation
    $errors = [];
    if (empty($full_name)) $errors[] = "Full name is required";
    if (empty($email)) $errors[] = "Email is required";
    if (empty($phone)) $errors[] = "Phone number is required";
    if (empty($date_of_birth)) $errors[] = "Date of birth is required";
    if (empty($gender)) $errors[] = "Gender is required";
    
    if (empty($errors)) {
        try {
            // Insert patient record
            $sql = "INSERT INTO patient (full_name, email, phone, date_of_birth, gender, address, emergency_contact, emergency_phone, medical_history, allergies, blood_type, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            $result = executeQuery($sql, "sssssssssss", [
                $full_name, $email, $phone, $date_of_birth, $gender, 
                $address, $emergency_contact, $emergency_phone, 
                $medical_history, $allergies, $blood_type
            ]);
            
            if ($result) {
                $_SESSION['flash_message'] = "Patient registered successfully!";
                $_SESSION['flash_type'] = "success";
                header("Location: patient_registration.php");
                exit;
            } else {
                $errors[] = "Failed to register patient. Please try again.";
            }
        } catch (Exception $e) {
            $errors[] = "Database error: " . $e->getMessage();
        }
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-xl shadow-lg p-8 mb-8">
        <div class="flex items-center">
            <i class="fas fa-user-plus text-4xl mr-4"></i>
            <div>
                <h1 class="text-3xl font-bold">Patient Registration</h1>
                <p class="text-blue-100 mt-1">Register new patients into the hospital system</p>
            </div>
        </div>
    </div>

    <!-- Display Errors -->
    <?php if (!empty($errors)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <ul class="list-disc list-inside">
                <?php foreach ($errors as $error): ?>
                    <li><?= htmlspecialchars($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Registration Form -->
    <div class="bg-white rounded-xl shadow-lg p-8">
        <form method="POST" class="space-y-6">
            <!-- Personal Information Section -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-user text-blue-600 mr-2"></i>
                    Personal Information
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Full Name *</label>
                        <input type="text" name="full_name" required 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Enter patient's full name"
                               value="<?= htmlspecialchars($_POST['full_name'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Email Address *</label>
                        <input type="email" name="email" required 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="<EMAIL>"
                               value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Phone Number *</label>
                        <input type="tel" name="phone" required 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="+234 xxx xxx xxxx"
                               value="<?= htmlspecialchars($_POST['phone'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Date of Birth *</label>
                        <input type="date" name="date_of_birth" required 
                               class="form-input border-gray-300 focus:border-blue-500"
                               value="<?= htmlspecialchars($_POST['date_of_birth'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Gender *</label>
                        <select name="gender" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Gender</option>
                            <option value="male" <?= ($_POST['gender'] ?? '') === 'male' ? 'selected' : '' ?>>Male</option>
                            <option value="female" <?= ($_POST['gender'] ?? '') === 'female' ? 'selected' : '' ?>>Female</option>
                            <option value="other" <?= ($_POST['gender'] ?? '') === 'other' ? 'selected' : '' ?>>Other</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Blood Type</label>
                        <select name="blood_type" class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Blood Type</option>
                            <option value="A+" <?= ($_POST['blood_type'] ?? '') === 'A+' ? 'selected' : '' ?>>A+</option>
                            <option value="A-" <?= ($_POST['blood_type'] ?? '') === 'A-' ? 'selected' : '' ?>>A-</option>
                            <option value="B+" <?= ($_POST['blood_type'] ?? '') === 'B+' ? 'selected' : '' ?>>B+</option>
                            <option value="B-" <?= ($_POST['blood_type'] ?? '') === 'B-' ? 'selected' : '' ?>>B-</option>
                            <option value="AB+" <?= ($_POST['blood_type'] ?? '') === 'AB+' ? 'selected' : '' ?>>AB+</option>
                            <option value="AB-" <?= ($_POST['blood_type'] ?? '') === 'AB-' ? 'selected' : '' ?>>AB-</option>
                            <option value="O+" <?= ($_POST['blood_type'] ?? '') === 'O+' ? 'selected' : '' ?>>O+</option>
                            <option value="O-" <?= ($_POST['blood_type'] ?? '') === 'O-' ? 'selected' : '' ?>>O-</option>
                        </select>
                    </div>
                </div>
                <div class="mt-6">
                    <label class="form-label text-gray-700">Address</label>
                    <textarea name="address" rows="3" 
                              class="form-input border-gray-300 focus:border-blue-500" 
                              placeholder="Enter patient's address"><?= htmlspecialchars($_POST['address'] ?? '') ?></textarea>
                </div>
            </div>

            <!-- Emergency Contact Section -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-phone text-red-600 mr-2"></i>
                    Emergency Contact
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Emergency Contact Name</label>
                        <input type="text" name="emergency_contact" 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Emergency contact full name"
                               value="<?= htmlspecialchars($_POST['emergency_contact'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Emergency Contact Phone</label>
                        <input type="tel" name="emergency_phone" 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="+234 xxx xxx xxxx"
                               value="<?= htmlspecialchars($_POST['emergency_phone'] ?? '') ?>">
                    </div>
                </div>
            </div>

            <!-- Medical Information Section -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-notes-medical text-green-600 mr-2"></i>
                    Medical Information
                </h3>
                <div class="space-y-6">
                    <div>
                        <label class="form-label text-gray-700">Medical History</label>
                        <textarea name="medical_history" rows="4" 
                                  class="form-input border-gray-300 focus:border-blue-500" 
                                  placeholder="Enter any relevant medical history, previous surgeries, chronic conditions, etc."><?= htmlspecialchars($_POST['medical_history'] ?? '') ?></textarea>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Allergies</label>
                        <textarea name="allergies" rows="3" 
                                  class="form-input border-gray-300 focus:border-blue-500" 
                                  placeholder="Enter any known allergies (medications, food, environmental, etc.)"><?= htmlspecialchars($_POST['allergies'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="window.history.back()" 
                        class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
                    Cancel
                </button>
                <button type="submit" 
                        class="btn-primary px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition">
                    <i class="fas fa-save mr-2"></i>
                    Register Patient
                </button>
            </div>
        </form>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
