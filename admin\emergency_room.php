<?php
/**
 * Emergency Room Management
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require admin login
requireLogin('../auth/admin_login.php');
requireRole('admin', '../auth/admin_login.php');

// Get patients and staff for dropdowns
$patients = [];
$doctors = [];

try {
    $patients = fetchAll("SELECT id, full_name, phone FROM patient ORDER BY full_name") ?: [];
    $doctors = fetchAll("SELECT id, full_name, specialty FROM staff WHERE role = 'doctor' ORDER BY full_name") ?: [];
} catch (Exception $e) {
    // Handle database errors gracefully
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $patient_id = $_POST['patient_id'] ?? '';
    $doctor_id = $_POST['doctor_id'] ?? '';
    $arrival_time = $_POST['arrival_time'] ?? '';
    $triage_level = $_POST['triage_level'] ?? '';
    $chief_complaint = $_POST['chief_complaint'] ?? '';
    $vital_signs = $_POST['vital_signs'] ?? '';
    $symptoms = $_POST['symptoms'] ?? '';
    $treatment_given = $_POST['treatment_given'] ?? '';
    $disposition = $_POST['disposition'] ?? '';
    $status = $_POST['status'] ?? 'waiting';
    
    // Validation
    $errors = [];
    if (empty($patient_id)) $errors[] = "Patient is required";
    if (empty($arrival_time)) $errors[] = "Arrival time is required";
    if (empty($triage_level)) $errors[] = "Triage level is required";
    if (empty($chief_complaint)) $errors[] = "Chief complaint is required";
    
    if (empty($errors)) {
        try {
            // Insert emergency room record
            $sql = "INSERT INTO emergency_room (patient_id, doctor_id, arrival_time, triage_level, chief_complaint, vital_signs, symptoms, treatment_given, disposition, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            $result = executeQuery($sql, "iissssssss", [
                $patient_id, $doctor_id, $arrival_time, $triage_level, $chief_complaint,
                $vital_signs, $symptoms, $treatment_given, $disposition, $status
            ]);
            
            if ($result) {
                $_SESSION['flash_message'] = "Emergency room entry created successfully!";
                $_SESSION['flash_type'] = "success";
                header("Location: emergency_room.php");
                exit;
            } else {
                $errors[] = "Failed to create emergency room entry. Please try again.";
            }
        } catch (Exception $e) {
            $errors[] = "Database error: " . $e->getMessage();
        }
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-red-600 to-orange-600 text-white rounded-xl shadow-lg p-8 mb-8">
        <div class="flex items-center">
            <i class="fas fa-ambulance text-4xl mr-4"></i>
            <div>
                <h1 class="text-3xl font-bold">Emergency Room</h1>
                <p class="text-red-100 mt-1">Manage emergency room patients and treatments</p>
            </div>
        </div>
    </div>

    <!-- Display Errors -->
    <?php if (!empty($errors)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <ul class="list-disc list-inside">
                <?php foreach ($errors as $error): ?>
                    <li><?= htmlspecialchars($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Emergency Room Form -->
    <div class="bg-white rounded-xl shadow-lg p-8">
        <form method="POST" class="space-y-6">
            <!-- Patient and Staff Information -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-user-injured text-red-600 mr-2"></i>
                    Patient & Staff Information
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Patient *</label>
                        <select name="patient_id" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Patient</option>
                            <?php foreach ($patients as $patient): ?>
                                <option value="<?= $patient['id'] ?>" <?= ($_POST['patient_id'] ?? '') == $patient['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($patient['full_name']) ?> (<?= htmlspecialchars($patient['phone']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Attending Doctor</label>
                        <select name="doctor_id" class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Doctor</option>
                            <?php foreach ($doctors as $doctor): ?>
                                <option value="<?= $doctor['id'] ?>" <?= ($_POST['doctor_id'] ?? '') == $doctor['id'] ? 'selected' : '' ?>>
                                    Dr. <?= htmlspecialchars($doctor['full_name']) ?>
                                    <?= $doctor['specialty'] ? '(' . htmlspecialchars($doctor['specialty']) . ')' : '' ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Triage Information -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-exclamation-triangle text-orange-600 mr-2"></i>
                    Triage Information
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Arrival Time *</label>
                        <input type="datetime-local" name="arrival_time" required 
                               class="form-input border-gray-300 focus:border-blue-500"
                               value="<?= htmlspecialchars($_POST['arrival_time'] ?? date('Y-m-d\TH:i')) ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Triage Level *</label>
                        <select name="triage_level" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Triage Level</option>
                            <option value="1" <?= ($_POST['triage_level'] ?? '') === '1' ? 'selected' : '' ?>>Level 1 - Immediate (Life-threatening)</option>
                            <option value="2" <?= ($_POST['triage_level'] ?? '') === '2' ? 'selected' : '' ?>>Level 2 - Urgent (High priority)</option>
                            <option value="3" <?= ($_POST['triage_level'] ?? '') === '3' ? 'selected' : '' ?>>Level 3 - Less Urgent (Moderate priority)</option>
                            <option value="4" <?= ($_POST['triage_level'] ?? '') === '4' ? 'selected' : '' ?>>Level 4 - Non-urgent (Low priority)</option>
                            <option value="5" <?= ($_POST['triage_level'] ?? '') === '5' ? 'selected' : '' ?>>Level 5 - Minor (Very low priority)</option>
                        </select>
                    </div>
                    <div class="md:col-span-2">
                        <label class="form-label text-gray-700">Chief Complaint *</label>
                        <textarea name="chief_complaint" rows="3" required
                                  class="form-input border-gray-300 focus:border-blue-500" 
                                  placeholder="Enter the main reason for the emergency visit"><?= htmlspecialchars($_POST['chief_complaint'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Medical Assessment -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-stethoscope text-blue-600 mr-2"></i>
                    Medical Assessment
                </h3>
                <div class="space-y-6">
                    <div>
                        <label class="form-label text-gray-700">Vital Signs</label>
                        <textarea name="vital_signs" rows="3" 
                                  class="form-input border-gray-300 focus:border-blue-500" 
                                  placeholder="Blood pressure, heart rate, temperature, respiratory rate, oxygen saturation"><?= htmlspecialchars($_POST['vital_signs'] ?? '') ?></textarea>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Symptoms</label>
                        <textarea name="symptoms" rows="4" 
                                  class="form-input border-gray-300 focus:border-blue-500" 
                                  placeholder="Detailed description of symptoms and patient condition"><?= htmlspecialchars($_POST['symptoms'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Treatment and Disposition -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-procedures text-green-600 mr-2"></i>
                    Treatment & Disposition
                </h3>
                <div class="space-y-6">
                    <div>
                        <label class="form-label text-gray-700">Treatment Given</label>
                        <textarea name="treatment_given" rows="4" 
                                  class="form-input border-gray-300 focus:border-blue-500" 
                                  placeholder="Describe treatments, medications, procedures performed"><?= htmlspecialchars($_POST['treatment_given'] ?? '') ?></textarea>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label text-gray-700">Disposition</label>
                            <select name="disposition" class="form-input border-gray-300 focus:border-blue-500">
                                <option value="">Select Disposition</option>
                                <option value="discharged" <?= ($_POST['disposition'] ?? '') === 'discharged' ? 'selected' : '' ?>>Discharged Home</option>
                                <option value="admitted" <?= ($_POST['disposition'] ?? '') === 'admitted' ? 'selected' : '' ?>>Admitted to Hospital</option>
                                <option value="transferred" <?= ($_POST['disposition'] ?? '') === 'transferred' ? 'selected' : '' ?>>Transferred to Another Facility</option>
                                <option value="observation" <?= ($_POST['disposition'] ?? '') === 'observation' ? 'selected' : '' ?>>Under Observation</option>
                                <option value="ama" <?= ($_POST['disposition'] ?? '') === 'ama' ? 'selected' : '' ?>>Left Against Medical Advice</option>
                            </select>
                        </div>
                        <div>
                            <label class="form-label text-gray-700">Status</label>
                            <select name="status" class="form-input border-gray-300 focus:border-blue-500">
                                <option value="waiting" <?= ($_POST['status'] ?? 'waiting') === 'waiting' ? 'selected' : '' ?>>Waiting</option>
                                <option value="in_treatment" <?= ($_POST['status'] ?? '') === 'in_treatment' ? 'selected' : '' ?>>In Treatment</option>
                                <option value="completed" <?= ($_POST['status'] ?? '') === 'completed' ? 'selected' : '' ?>>Completed</option>
                                <option value="discharged" <?= ($_POST['status'] ?? '') === 'discharged' ? 'selected' : '' ?>>Discharged</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="window.history.back()" 
                        class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
                    Cancel
                </button>
                <button type="submit" 
                        class="btn-primary px-8 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition">
                    <i class="fas fa-save mr-2"></i>
                    Save Emergency Record
                </button>
            </div>
        </form>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
