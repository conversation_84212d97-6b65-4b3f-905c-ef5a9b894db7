<?php
/**
 * User/Patient Registration Page
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Redirect if already logged in
if (isLoggedIn()) {
    if (hasRole('patient')) {
        header("Location: ../user/dashboard.php");
    } else {
        header("Location: ../admin/dashboard.php");
    }
    exit;
}

// Process registration form
$error = '';
$success = false;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $email = trim($_POST['email'] ?? '');
    $fullName = trim($_POST['full_name'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $gender = $_POST['gender'] ?? '';
    $dob = $_POST['dob'] ?? '';
    
    // Validate required fields
    if (empty($username) || empty($password) || empty($confirm_password) || empty($email) || 
        empty($fullName) || empty($phone) || empty($address) || empty($gender) || empty($dob)) {
        $error = "All fields are required";
    } 
    // Validate password match
    else if ($password !== $confirm_password) {
        $error = "Passwords do not match";
    } 
    // Validate password length
    else if (strlen($password) < 6) {
        $error = "Password must be at least 6 characters long";
    } 
    // Validate email format
    else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } 
    // Validate phone number (basic validation)
    else if (!preg_match("/^[0-9+\-\s()]{7,15}$/", $phone)) {
        $error = "Invalid phone number format";
    } 
    // Validate date of birth (must be in the past)
    else if (strtotime($dob) > time()) {
        $error = "Date of birth must be in the past";
    } 
    else {
        // Attempt registration
        $result = registerUser($username, $password, $email, $fullName, $phone, $address, $gender, $dob);
        
        if ($result === true) {
            $success = true;
        } else {
            $error = $result;
        }
    }
}

// Include header
include_once '../includes/header.php';
?>

<?php if ($success): ?>
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden mt-10">
        <div class="bg-green-600 text-white py-4 px-6">
            <h2 class="text-xl font-semibold">Registration Successful</h2>
        </div>
        
        <div class="py-6 px-8">
            <p class="mb-4">Your account has been created successfully!</p>
            <p class="mb-6">You can now <a href="user_login.php" class="text-blue-600 hover:underline">login</a> to access your account.</p>
            
            <a href="user_login.php" class="block w-full bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition">
                Go to Login
            </a>
        </div>
    </div>
<?php else: ?>
    <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-md overflow-hidden mt-10 mb-10">
        <div class="bg-blue-600 text-white py-4 px-6">
            <h2 class="text-xl font-semibold">Patient Registration</h2>
        </div>
        
        <form method="POST" action="" class="py-6 px-8">
            <?php if (!empty($error)): ?>
                <div class="mb-4 p-3 bg-red-100 text-red-800 rounded">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Account Information -->
                <div class="md:col-span-2">
                    <h3 class="text-lg font-semibold mb-3 text-gray-700 border-b pb-2">Account Information</h3>
                </div>
                
                <div class="mb-4">
                    <label for="username" class="block text-gray-700 font-medium mb-2">Username</label>
                    <input type="text" id="username" name="username" value="<?= htmlspecialchars($username ?? '') ?>" 
                           class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                           required>
                </div>
                
                <div class="mb-4">
                    <label for="email" class="block text-gray-700 font-medium mb-2">Email</label>
                    <input type="email" id="email" name="email" value="<?= htmlspecialchars($email ?? '') ?>" 
                           class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                           required>
                </div>
                
                <div class="mb-4">
                    <label for="password" class="block text-gray-700 font-medium mb-2">Password</label>
                    <input type="password" id="password" name="password" 
                           class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                           required>
                </div>
                
                <div class="mb-4">
                    <label for="confirm_password" class="block text-gray-700 font-medium mb-2">Confirm Password</label>
                    <input type="password" id="confirm_password" name="confirm_password" 
                           class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                           required>
                </div>
                
                <!-- Personal Information -->
                <div class="md:col-span-2 mt-4">
                    <h3 class="text-lg font-semibold mb-3 text-gray-700 border-b pb-2">Personal Information</h3>
                </div>
                
                <div class="mb-4">
                    <label for="full_name" class="block text-gray-700 font-medium mb-2">Full Name</label>
                    <input type="text" id="full_name" name="full_name" value="<?= htmlspecialchars($fullName ?? '') ?>" 
                           class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                           required>
                </div>
                
                <div class="mb-4">
                    <label for="phone" class="block text-gray-700 font-medium mb-2">Phone Number</label>
                    <input type="tel" id="phone" name="phone" value="<?= htmlspecialchars($phone ?? '') ?>" 
                           class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                           required>
                </div>
                
                <div class="mb-4">
                    <label for="gender" class="block text-gray-700 font-medium mb-2">Gender</label>
                    <select id="gender" name="gender" 
                            class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                            required>
                        <option value="" disabled <?= empty($gender) ? 'selected' : '' ?>>Select Gender</option>
                        <option value="Male" <?= ($gender ?? '') === 'Male' ? 'selected' : '' ?>>Male</option>
                        <option value="Female" <?= ($gender ?? '') === 'Female' ? 'selected' : '' ?>>Female</option>
                        <option value="Other" <?= ($gender ?? '') === 'Other' ? 'selected' : '' ?>>Other</option>
                    </select>
                </div>
                
                <div class="mb-4">
                    <label for="dob" class="block text-gray-700 font-medium mb-2">Date of Birth</label>
                    <input type="date" id="dob" name="dob" value="<?= htmlspecialchars($dob ?? '') ?>" 
                           class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                           required>
                </div>
                
                <div class="mb-4 md:col-span-2">
                    <label for="address" class="block text-gray-700 font-medium mb-2">Address</label>
                    <textarea id="address" name="address" rows="3" 
                              class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                              required><?= htmlspecialchars($address ?? '') ?></textarea>
                </div>
            </div>
            
            <div class="mt-6">
                <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition">
                    Register
                </button>
            </div>
            
            <div class="mt-4 text-center">
                <p class="text-gray-600">Already have an account? <a href="user_login.php" class="text-blue-600 hover:underline">Login here</a></p>
            </div>
        </form>
    </div>
<?php endif; ?>

<?php include_once '../includes/footer.php'; ?>
