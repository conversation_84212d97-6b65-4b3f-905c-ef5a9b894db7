<?php
/**
 * Medical History Page
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require patient login
requireLogin('../auth/user_login.php');
requireRole('patient', '../auth/user_login.php');

// Get patient information
$userId = $_SESSION['user_id'];
$patient = fetchOne("SELECT * FROM patient WHERE user_id = ?", "i", [$userId]);

// Get medical history (medications)
$medications = fetchAll(
    "SELECT m.*, s.full_name as doctor_name 
     FROM medication m 
     JOIN staff s ON m.staff_id = s.id 
     WHERE m.patient_id = ? 
     ORDER BY m.prescribed_date DESC", 
    "i", [$patient['id']]
);

// Include header
include_once '../includes/header.php';
?>

<div class="max-w-6xl mx-auto">
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">Medical History</h1>
        <p class="text-gray-600">View your medical records and prescribed medications.</p>
    </div>
    
    <!-- Medications -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Prescribed Medications</h2>
        
        <?php if (empty($medications)): ?>
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-gray-500 italic">No medication records found.</p>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-2 text-left text-gray-700">Date</th>
                            <th class="px-4 py-2 text-left text-gray-700">Medication</th>
                            <th class="px-4 py-2 text-left text-gray-700">Dosage</th>
                            <th class="px-4 py-2 text-left text-gray-700">Frequency</th>
                            <th class="px-4 py-2 text-left text-gray-700">Duration</th>
                            <th class="px-4 py-2 text-left text-gray-700">Prescribed By</th>
                            <th class="px-4 py-2 text-left text-gray-700">Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($medications as $medication): ?>
                            <tr class="border-t">
                                <td class="px-4 py-3"><?= date('M j, Y', strtotime($medication['prescribed_date'])) ?></td>
                                <td class="px-4 py-3 font-medium"><?= htmlspecialchars($medication['medication_name']) ?></td>
                                <td class="px-4 py-3"><?= htmlspecialchars($medication['dosage']) ?></td>
                                <td class="px-4 py-3"><?= htmlspecialchars($medication['frequency']) ?></td>
                                <td class="px-4 py-3"><?= htmlspecialchars($medication['duration']) ?></td>
                                <td class="px-4 py-3">Dr. <?= htmlspecialchars($medication['doctor_name']) ?></td>
                                <td class="px-4 py-3"><?= htmlspecialchars($medication['notes']) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Medical Records -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Allergies -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Allergies</h2>
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-gray-500 italic">No allergy records found.</p>
                <p class="mt-2 text-sm text-gray-600">Please inform your doctor about any allergies during your next visit.</p>
            </div>
        </div>
        
        <!-- Chronic Conditions -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Chronic Conditions</h2>
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-gray-500 italic">No chronic condition records found.</p>
            </div>
        </div>
    </div>
    
    <!-- Past Visits -->
    <div class="bg-white rounded-lg shadow-md p-6 mt-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Past Visits</h2>
        
        <?php
        // Get completed appointments
        $pastVisits = fetchAll(
            "SELECT a.*, s.full_name as doctor_name, s.specialty 
             FROM appointment a 
             JOIN staff s ON a.staff_id = s.id 
             WHERE a.patient_id = ? AND a.status = 'completed' 
             ORDER BY a.appointment_date DESC", 
            "i", [$patient['id']]
        );
        ?>
        
        <?php if (empty($pastVisits)): ?>
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-gray-500 italic">No past visit records found.</p>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-2 text-left text-gray-700">Date</th>
                            <th class="px-4 py-2 text-left text-gray-700">Doctor</th>
                            <th class="px-4 py-2 text-left text-gray-700">Department</th>
                            <th class="px-4 py-2 text-left text-gray-700">Reason</th>
                            <th class="px-4 py-2 text-left text-gray-700">Diagnosis</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($pastVisits as $visit): ?>
                            <tr class="border-t">
                                <td class="px-4 py-3"><?= date('M j, Y', strtotime($visit['appointment_date'])) ?></td>
                                <td class="px-4 py-3">Dr. <?= htmlspecialchars($visit['doctor_name']) ?></td>
                                <td class="px-4 py-3"><?= htmlspecialchars($visit['specialty']) ?></td>
                                <td class="px-4 py-3"><?= htmlspecialchars($visit['reason']) ?></td>
                                <td class="px-4 py-3"><?= htmlspecialchars($visit['diagnosis'] ?? 'Not recorded') ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Health Metrics -->
    <div class="bg-white rounded-lg shadow-md p-6 mt-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Health Metrics</h2>
        
        <div class="bg-gray-50 p-4 rounded-lg">
            <p class="text-gray-500 italic">No health metrics recorded yet.</p>
            <p class="mt-2 text-sm text-gray-600">Health metrics will be updated during your next check-up.</p>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
