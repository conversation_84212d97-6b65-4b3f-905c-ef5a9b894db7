<?php
/**
 * Authentication Functions
 *
 * This file contains functions related to user authentication for the UBTH Hospital Management System.
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database connection if not already included
if (!function_exists('executeQuery')) {
    require_once 'db.php';
}

/**
 * Register a new user/patient
 *
 * @param string $username Username
 * @param string $password Password
 * @param string $email Email
 * @param string $fullName Full name
 * @param string $phone Phone number
 * @param string $address Address
 * @param string $gender Gender
 * @param string $dob Date of birth
 * @return bool|string Returns true on success or error message on failure
 */
function registerUser($username, $password, $email, $fullName, $phone, $address, $gender, $dob) {
    // Check if username already exists
    $existingUser = fetchOne("SELECT id FROM users WHERE username = ?", "s", [$username]);
    if ($existingUser) {
        return "Username already exists";
    }

    // Check if email already exists
    $existingEmail = fetchOne("SELECT id FROM users WHERE email = ?", "s", [$email]);
    if ($existingEmail) {
        return "Email already exists";
    }

    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

    // Insert new user
    $sql = "INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, 'patient')";
    $stmt = executeQuery($sql, "sss", [$username, $hashedPassword, $email]);

    if ($stmt === false) {
        return "Database error: " . $GLOBALS['conn']->error;
    }

    $userId = $GLOBALS['conn']->insert_id;

    // Insert patient details
    $sql = "INSERT INTO patient (user_id, full_name, phone, address, gender, date_of_birth)
            VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = executeQuery($sql, "isssss", [$userId, $fullName, $phone, $address, $gender, $dob]);

    if ($stmt === false) {
        // Rollback user creation
        executeQuery("DELETE FROM users WHERE id = ?", "i", [$userId]);
        return "Database error: " . $GLOBALS['conn']->error;
    }

    return true;
}

/**
 * Login a user
 *
 * @param string $username Username
 * @param string $password Password
 * @param string $role Role (admin or patient)
 * @return bool|string Returns true on success or error message on failure
 */
function loginUser($username, $password, $role = 'patient') {
    // Get user by username
    $user = fetchOne("SELECT id, username, password, role FROM users WHERE username = ? AND role = ?",
                    "ss", [$username, $role]);

    if (!$user) {
        return "Invalid username or password";
    }

    // Verify password
    if (!password_verify($password, $user['password'])) {
        return "Invalid username or password";
    }

    // Set session variables
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['role'] = $user['role'];

    // Get additional user details based on role
    if ($role === 'patient') {
        $patient = fetchOne("SELECT * FROM patient WHERE user_id = ?", "i", [$user['id']]);
        if ($patient) {
            $_SESSION['full_name'] = $patient['full_name'];
        }
    } else if ($role === 'admin') {
        $staff = fetchOne("SELECT * FROM staff WHERE user_id = ?", "i", [$user['id']]);
        if ($staff) {
            $_SESSION['full_name'] = $staff['full_name'];
            $_SESSION['staff_id'] = $staff['id'];
        }
    }

    return true;
}

/**
 * Check if user is logged in
 *
 * @return bool Returns true if user is logged in, false otherwise
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * Check if user is the default admin
 *
 * @return bool Returns true if user is the default admin, false otherwise
 */
function isDefaultAdmin() {
    return isLoggedIn() && $_SESSION['username'] === 'ubthadmin' && $_SESSION['user_id'] === 1;
}

/**
 * Check if user has a specific role
 *
 * @param string $role Role to check
 * @return bool Returns true if user has the specified role, false otherwise
 */
function hasRole($role) {
    // Default admin always has admin role
    if ($role === 'admin' && isDefaultAdmin()) {
        return true;
    }
    return isLoggedIn() && $_SESSION['role'] === $role;
}

/**
 * Redirect if user is not logged in
 *
 * @param string $redirectUrl URL to redirect to
 */
function requireLogin($redirectUrl = '/auth/user_login.php') {
    if (!isLoggedIn()) {
        header("Location: $redirectUrl");
        exit;
    }
}

/**
 * Redirect if user does not have a specific role
 *
 * @param string $role Role to check
 * @param string $redirectUrl URL to redirect to
 */
function requireRole($role, $redirectUrl = '/auth/user_login.php') {
    if (!hasRole($role)) {
        header("Location: $redirectUrl");
        exit;
    }
}

/**
 * Logout user
 */
function logoutUser() {
    // Unset all session variables
    $_SESSION = [];

    // Destroy the session
    session_destroy();
}
?>
