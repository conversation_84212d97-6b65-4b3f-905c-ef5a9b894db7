<?php
/**
 * Database Connection
 * 
 * This file establishes a connection to the MySQL database for the UBTH Hospital Management System.
 */

// Database configuration
$db_host = 'localhost';
$db_user = 'root';
$db_password = '';
$db_name = 'ubth_hospitaldb';

// Create connection
$conn = new mysqli($db_host, $db_user, $db_password, $db_name);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set character set
$conn->set_charset("utf8mb4");

/**
 * Helper function to execute queries safely
 * 
 * @param string $sql SQL query with placeholders
 * @param string $types Types of parameters (i: integer, s: string, d: double, b: blob)
 * @param array $params Array of parameters to bind
 * @return mysqli_stmt|false Returns the prepared statement or false on failure
 */
function executeQuery($sql, $types = "", $params = []) {
    global $conn;
    
    $stmt = $conn->prepare($sql);
    
    if ($stmt === false) {
        return false;
    }
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    return $stmt;
}

/**
 * Helper function to fetch all rows from a query
 * 
 * @param string $sql SQL query with placeholders
 * @param string $types Types of parameters
 * @param array $params Array of parameters to bind
 * @return array|false Returns array of results or false on failure
 */
function fetchAll($sql, $types = "", $params = []) {
    $stmt = executeQuery($sql, $types, $params);
    
    if ($stmt === false) {
        return false;
    }
    
    $result = $stmt->get_result();
    $data = [];
    
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
    
    $stmt->close();
    return $data;
}

/**
 * Helper function to fetch a single row from a query
 * 
 * @param string $sql SQL query with placeholders
 * @param string $types Types of parameters
 * @param array $params Array of parameters to bind
 * @return array|false Returns a single row or false on failure
 */
function fetchOne($sql, $types = "", $params = []) {
    $stmt = executeQuery($sql, $types, $params);
    
    if ($stmt === false) {
        return false;
    }
    
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    $stmt->close();
    return $row;
}
?>
