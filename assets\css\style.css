/* Custom styles for UBTH Hospital Management System */

/* Custom Colors */
:root {
  --primary-color: #00a86b; /* Medical green */
  --primary-dark: #008c58;
  --primary-light: #7fdcad;
  --secondary-color: #0077cc; /* Medical blue */
  --secondary-dark: #005ea3;
  --secondary-light: #4da3ff;
  --accent-color: #ff6b6b; /* Accent red for important elements */
  --light-bg: #f0f8ff; /* Light blue background */
  --dark-text: #2c3e50;
  --light-text: #ffffff;
  --gray-light: #f8f9fa;
  --gray-medium: #e9ecef;
  --gray-dark: #6c757d;
}

/* General Styles */
body {
  background-color: var(--light-bg);
  color: var(--dark-text);
  font-family: 'Poppins', sans-serif;
}

/* Header Styles */
header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-img {
  height: 50px;
  margin-right: 10px;
}

.logo-text {
  font-weight: 700;
  color: var(--light-text);
}

/* Navigation */
nav a {
  transition: all 0.3s ease;
  border-radius: 0.375rem;
}

nav a:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.active-nav {
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  font-weight: 600;
}

/* Buttons */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--light-text);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  color: var(--light-text);
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background-color: var(--secondary-dark);
  border-color: var(--secondary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Cards */
.card {
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1519494026892-80bbd2d6fd0d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1253&q=80');
  background-size: cover;
  background-position: center;
  color: var(--light-text);
  position: relative;
  min-height: 500px;
  display: flex;
  align-items: center;
}

/* Services Section */
.service-card {
  border-left: 4px solid var(--primary-color);
  transition: all 0.3s ease;
}

.service-card:hover {
  border-left-color: var(--secondary-color);
  background-color: var(--gray-light);
}

.service-icon {
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.service-card:hover .service-icon {
  color: var(--secondary-color);
  transform: scale(1.1);
}

/* Dashboard Cards */
.dashboard-stat {
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.dashboard-stat:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

/* Footer */
footer {
  background: linear-gradient(135deg, var(--secondary-dark), var(--primary-dark));
  color: var(--light-text);
}

footer a {
  color: var(--light-text);
  transition: all 0.3s ease;
}

footer a:hover {
  color: var(--primary-light);
  text-decoration: none;
}

/* Form Elements */
input, select, textarea {
  border-radius: 0.375rem !important;
  border: 1px solid var(--gray-medium) !important;
  transition: all 0.3s ease !important;
}

input:focus, select:focus, textarea:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 168, 107, 0.25) !important;
}

/* Tables */
table {
  border-radius: 0.5rem;
  overflow: hidden;
}

table thead {
  background-color: var(--primary-color);
  color: var(--light-text);
}

/* Status Badges */
.status-scheduled {
  background-color: var(--secondary-light);
  color: var(--secondary-dark);
}

.status-completed {
  background-color: var(--primary-light);
  color: var(--primary-dark);
}

.status-cancelled {
  background-color: var(--accent-color);
  color: white;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-in-up {
  animation: slideInUp 0.5s ease-out;
}

/* Loading Spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 168, 107, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .logo-img {
    height: 40px;
  }

  .hero-section {
    padding: 3rem 1rem;
  }
}

@media (max-width: 576px) {
  .logo-img {
    height: 35px;
  }
}
