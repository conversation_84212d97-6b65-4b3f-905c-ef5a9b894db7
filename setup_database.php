<?php
/**
 * UBTH Hospital Management System Database Setup
 * 
 * This script sets up the database and tables for the UBTH Hospital Management System.
 * It also creates a default admin user with username 'ubthadmin' and password 'church123'.
 */

// Database configuration
$db_host = 'localhost';
$db_user = 'root';
$db_password = '';
$db_name = 'ubth_hospitaldb';

// Create connection
$conn = new mysqli($db_host, $db_user, $db_password);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Create database
$sql = "CREATE DATABASE IF NOT EXISTS `$db_name`";
if ($conn->query($sql) === TRUE) {
    echo "Database created successfully or already exists<br>";
} else {
    die("Error creating database: " . $conn->error);
}

// Select database
$conn->select_db($db_name);

// Create users table
$sql = "CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `role` enum('admin','patient') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if ($conn->query($sql) === TRUE) {
    echo "Users table created successfully or already exists<br>";
} else {
    echo "Error creating users table: " . $conn->error . "<br>";
}

// Create patient table
$sql = "CREATE TABLE IF NOT EXISTS `patient` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `address` text NOT NULL,
  `gender` enum('Male','Female','Other') NOT NULL,
  `date_of_birth` date NOT NULL,
  `blood_group` varchar(10) DEFAULT NULL,
  `allergies` text DEFAULT NULL,
  `medical_conditions` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `patient_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if ($conn->query($sql) === TRUE) {
    echo "Patient table created successfully or already exists<br>";
} else {
    echo "Error creating patient table: " . $conn->error . "<br>";
}

// Create staff table
$sql = "CREATE TABLE IF NOT EXISTS `staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `address` text NOT NULL,
  `gender` enum('Male','Female','Other') NOT NULL,
  `date_of_birth` date NOT NULL,
  `role` enum('doctor','nurse','admin','receptionist') NOT NULL,
  `specialty` varchar(100) DEFAULT NULL,
  `qualification` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `staff_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if ($conn->query($sql) === TRUE) {
    echo "Staff table created successfully or already exists<br>";
} else {
    echo "Error creating staff table: " . $conn->error . "<br>";
}

// Create appointment table
$sql = "CREATE TABLE IF NOT EXISTS `appointment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `patient_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `appointment_date` date NOT NULL,
  `appointment_time` time NOT NULL,
  `reason` text NOT NULL,
  `status` enum('scheduled','completed','cancelled') NOT NULL DEFAULT 'scheduled',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `patient_id` (`patient_id`),
  KEY `staff_id` (`staff_id`),
  CONSTRAINT `appointment_ibfk_1` FOREIGN KEY (`patient_id`) REFERENCES `patient` (`id`) ON DELETE CASCADE,
  CONSTRAINT `appointment_ibfk_2` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if ($conn->query($sql) === TRUE) {
    echo "Appointment table created successfully or already exists<br>";
} else {
    echo "Error creating appointment table: " . $conn->error . "<br>";
}

// Create medical_history table
$sql = "CREATE TABLE IF NOT EXISTS `medical_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `patient_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `diagnosis` text NOT NULL,
  `treatment` text NOT NULL,
  `notes` text DEFAULT NULL,
  `date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `patient_id` (`patient_id`),
  KEY `staff_id` (`staff_id`),
  CONSTRAINT `medical_history_ibfk_1` FOREIGN KEY (`patient_id`) REFERENCES `patient` (`id`) ON DELETE CASCADE,
  CONSTRAINT `medical_history_ibfk_2` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if ($conn->query($sql) === TRUE) {
    echo "Medical history table created successfully or already exists<br>";
} else {
    echo "Error creating medical history table: " . $conn->error . "<br>";
}

// Create medication table
$sql = "CREATE TABLE IF NOT EXISTS `medication` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `patient_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `medication_name` varchar(100) NOT NULL,
  `dosage` varchar(50) NOT NULL,
  `frequency` varchar(50) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `prescribed_date` date NOT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `patient_id` (`patient_id`),
  KEY `staff_id` (`staff_id`),
  CONSTRAINT `medication_ibfk_1` FOREIGN KEY (`patient_id`) REFERENCES `patient` (`id`) ON DELETE CASCADE,
  CONSTRAINT `medication_ibfk_2` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if ($conn->query($sql) === TRUE) {
    echo "Medication table created successfully or already exists<br>";
} else {
    echo "Error creating medication table: " . $conn->error . "<br>";
}

// Create department table
$sql = "CREATE TABLE IF NOT EXISTS `department` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if ($conn->query($sql) === TRUE) {
    echo "Department table created successfully or already exists<br>";
} else {
    echo "Error creating department table: " . $conn->error . "<br>";
}

// Create staff_department table
$sql = "CREATE TABLE IF NOT EXISTS `staff_department` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `department_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `staff_department_unique` (`staff_id`, `department_id`),
  KEY `department_id` (`department_id`),
  CONSTRAINT `staff_department_ibfk_1` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE CASCADE,
  CONSTRAINT `staff_department_ibfk_2` FOREIGN KEY (`department_id`) REFERENCES `department` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if ($conn->query($sql) === TRUE) {
    echo "Staff department table created successfully or already exists<br>";
} else {
    echo "Error creating staff department table: " . $conn->error . "<br>";
}

// Check if default admin user exists
$stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
$username = 'ubthadmin';
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    // Create default admin user
    $stmt = $conn->prepare("INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, ?)");
    $password = password_hash('church123', PASSWORD_DEFAULT);
    $email = '<EMAIL>';
    $role = 'admin';
    $stmt->bind_param("ssss", $username, $password, $email, $role);
    
    if ($stmt->execute()) {
        $userId = $stmt->insert_id;
        echo "Default admin user created successfully<br>";
        
        // Create admin staff record
        $stmt = $conn->prepare("INSERT INTO staff (user_id, full_name, phone, address, gender, date_of_birth, role, specialty) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $fullName = 'UBTH Administrator';
        $phone = '08012345678';
        $address = 'UBTH, Benin City';
        $gender = 'Male';
        $dob = '1980-01-01';
        $staffRole = 'admin';
        $specialty = 'Hospital Administration';
        $stmt->bind_param("isssssss", $userId, $fullName, $phone, $address, $gender, $dob, $staffRole, $specialty);
        
        if ($stmt->execute()) {
            echo "Default admin staff record created successfully<br>";
        } else {
            echo "Error creating default admin staff record: " . $stmt->error . "<br>";
        }
    } else {
        echo "Error creating default admin user: " . $stmt->error . "<br>";
    }
} else {
    echo "Default admin user already exists<br>";
}

// Insert default departments if they don't exist
$departments = [
    ['General Medicine', 'Department for general medical care and consultations'],
    ['Cardiology', 'Department specializing in heart-related conditions'],
    ['Pediatrics', 'Department for child healthcare'],
    ['Orthopedics', 'Department for bone and joint related conditions'],
    ['Gynecology', 'Department for women\'s health'],
    ['Neurology', 'Department for nervous system disorders'],
    ['Dermatology', 'Department for skin-related conditions'],
    ['Ophthalmology', 'Department for eye care'],
    ['ENT', 'Department for Ear, Nose, and Throat'],
    ['Psychiatry', 'Department for mental health']
];

$stmt = $conn->prepare("SELECT id FROM department WHERE name = ?");
$insertStmt = $conn->prepare("INSERT INTO department (name, description) VALUES (?, ?)");

foreach ($departments as $dept) {
    $stmt->bind_param("s", $dept[0]);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        $insertStmt->bind_param("ss", $dept[0], $dept[1]);
        if ($insertStmt->execute()) {
            echo "Department '{$dept[0]}' created successfully<br>";
        } else {
            echo "Error creating department '{$dept[0]}': " . $insertStmt->error . "<br>";
        }
    } else {
        echo "Department '{$dept[0]}' already exists<br>";
    }
}

echo "<br><strong>Database setup completed successfully!</strong><br>";
echo "You can now log in with the default admin credentials:<br>";
echo "Username: ubthadmin<br>";
echo "Password: church123<br>";

$conn->close();
?>
