    </main>

    <!-- Footer -->
    <footer style="background: linear-gradient(135deg, #005ea3, #008c58) !important; color: white;" class="py-8 mt-auto">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Contact Info -->
                <div class="slide-in-up">
                    <h3 class="text-lg font-semibold mb-3 flex items-center">
                        <i class="fas fa-map-marker-alt mr-2"></i> Contact Us
                    </h3>
                    <div class="flex items-start mb-2">
                        <i class="fas fa-hospital mt-1 mr-2 text-primary-light"></i>
                        <p>University of Benin Teaching Hospital</p>
                    </div>
                    <div class="flex items-start mb-2">
                        <i class="fas fa-envelope mt-1 mr-2 text-primary-light"></i>
                        <p>P.M.B 1111, Ugbowo, Benin City</p>
                    </div>
                    <div class="flex items-start mb-2">
                        <i class="fas fa-map mt-1 mr-2 text-primary-light"></i>
                        <p>Edo State, Nigeria</p>
                    </div>
                    <div class="flex items-start mb-2">
                        <i class="fas fa-phone mt-1 mr-2 text-primary-light"></i>
                        <p>+234 (0) ************</p>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-at mt-1 mr-2 text-primary-light"></i>
                        <p><EMAIL></p>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="slide-in-up" style="animation-delay: 0.1s;">
                    <h3 class="text-lg font-semibold mb-3 flex items-center">
                        <i class="fas fa-link mr-2"></i> Quick Links
                    </h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="index.php" class="hover:underline flex items-center">
                                <i class="fas fa-chevron-right mr-2 text-primary-light"></i> Home
                            </a>
                        </li>
                        <li>
                            <a href="#" class="hover:underline flex items-center">
                                <i class="fas fa-chevron-right mr-2 text-primary-light"></i> About Us
                            </a>
                        </li>
                        <li>
                            <a href="#" class="hover:underline flex items-center">
                                <i class="fas fa-chevron-right mr-2 text-primary-light"></i> Services
                            </a>
                        </li>
                        <li>
                            <a href="#" class="hover:underline flex items-center">
                                <i class="fas fa-chevron-right mr-2 text-primary-light"></i> Doctors
                            </a>
                        </li>
                        <li>
                            <a href="#" class="hover:underline flex items-center">
                                <i class="fas fa-chevron-right mr-2 text-primary-light"></i> Contact
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Emergency Contact -->
                <div class="slide-in-up" style="animation-delay: 0.2s;">
                    <h3 class="text-lg font-semibold mb-3 flex items-center">
                        <i class="fas fa-ambulance mr-2"></i> Emergency
                    </h3>
                    <div class="bg-accent bg-opacity-20 p-4 rounded-lg">
                        <p class="mb-2">For emergencies, please call:</p>
                        <p class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-phone-alt mr-2"></i> +234 (0) ************
                        </p>
                        <p class="mb-2 flex items-center">
                            <i class="fas fa-clock mr-2"></i> Available 24/7
                        </p>
                    </div>
                </div>
            </div>

            <div class="border-t border-opacity-20 mt-6 pt-6 text-center">
                <div class="flex justify-center space-x-4 mb-4">
                    <a href="#" class="text-white hover:text-primary-light transition">
                        <i class="fab fa-facebook-f text-xl"></i>
                    </a>
                    <a href="#" class="text-white hover:text-primary-light transition">
                        <i class="fab fa-twitter text-xl"></i>
                    </a>
                    <a href="#" class="text-white hover:text-primary-light transition">
                        <i class="fab fa-instagram text-xl"></i>
                    </a>
                    <a href="#" class="text-white hover:text-primary-light transition">
                        <i class="fab fa-linkedin-in text-xl"></i>
                    </a>
                </div>
                <p>&copy; <?= date('Y') ?> University of Benin Teaching Hospital. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- AOS Animation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>

    <!-- JavaScript for mobile menu toggle and animations -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS
            AOS.init({
                duration: 1500,  // Very slow animation (1.5 seconds)
                easing: 'ease-out-cubic',
                once: false,     // Animation repeats when scrolling up/down
                mirror: true,    // Elements animate out when scrolling past them
                offset: 100      // Offset (in px) from the original trigger point
            });

            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');

            mobileMenuButton.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });

            // Add fade-in animation to main content
            const mainContent = document.querySelector('main');
            mainContent.classList.add('fade-in');

            // Add animation to cards
            const cards = document.querySelectorAll('.card, .dashboard-stat, .service-card');
            cards.forEach(card => {
                card.classList.add('slide-in-up');
            });

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    if (targetId === '#') return;

                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 100,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
