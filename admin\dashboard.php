<?php
/**
 * Admin Dashboard
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require admin login
requireLogin('../auth/admin_login.php');
requireRole('admin', '../auth/admin_login.php');

// Get admin information
$userId = $_SESSION['user_id'];
$staff = null;

// Only try to fetch from database if not using default admin
if ($userId != 1 || $_SESSION['username'] != 'ubthadmin') {
    $staff = fetchOne("SELECT * FROM staff WHERE user_id = ?", "i", [$userId]);
}

// Initialize variables with default values
$recentAppointments = [];
$totalAppointments = 0;
$todayAppointments = 0;
$pendingAppointments = 0;
$completedAppointments = 0;
$totalPatients = 0;
$newPatientsThisMonth = 0;
$totalStaff = 0;
$doctorsCount = 0;
$nursesCount = 0;

// Only try to fetch data if not using default admin or if database is available
try {
    // Check if the appointment table exists
    $tableExists = fetchOne("SHOW TABLES LIKE 'appointment'");

    if ($tableExists) {
        // Get recent appointments
        $recentAppointments = fetchAll(
            "SELECT a.*, p.full_name as patient_name, s.full_name as doctor_name, s.specialty
             FROM appointment a
             JOIN patient p ON a.patient_id = p.id
             JOIN staff s ON a.staff_id = s.id
             ORDER BY a.appointment_date DESC, a.appointment_time DESC
             LIMIT 10"
        ) ?: [];

        // Get appointment statistics
        $totalAppointmentsResult = fetchOne("SELECT COUNT(*) as total FROM appointment");
        $totalAppointments = $totalAppointmentsResult ? $totalAppointmentsResult['total'] : 0;

        $todayAppointmentsResult = fetchOne("SELECT COUNT(*) as total FROM appointment WHERE appointment_date = CURDATE()");
        $todayAppointments = $todayAppointmentsResult ? $todayAppointmentsResult['total'] : 0;

        $pendingAppointmentsResult = fetchOne("SELECT COUNT(*) as total FROM appointment WHERE status = 'scheduled'");
        $pendingAppointments = $pendingAppointmentsResult ? $pendingAppointmentsResult['total'] : 0;

        $completedAppointmentsResult = fetchOne("SELECT COUNT(*) as total FROM appointment WHERE status = 'completed'");
        $completedAppointments = $completedAppointmentsResult ? $completedAppointmentsResult['total'] : 0;
    }

    // Check if the patient table exists
    $tableExists = fetchOne("SHOW TABLES LIKE 'patient'");

    if ($tableExists) {
        // Get patient statistics
        $totalPatientsResult = fetchOne("SELECT COUNT(*) as total FROM patient");
        $totalPatients = $totalPatientsResult ? $totalPatientsResult['total'] : 0;

        $newPatientsThisMonthResult = fetchOne(
            "SELECT COUNT(*) as total FROM patient
             WHERE MONTH(created_at) = MONTH(CURRENT_DATE())
             AND YEAR(created_at) = YEAR(CURRENT_DATE())"
        );
        $newPatientsThisMonth = $newPatientsThisMonthResult ? $newPatientsThisMonthResult['total'] : 0;
    }

    // Check if the staff table exists
    $tableExists = fetchOne("SHOW TABLES LIKE 'staff'");

    if ($tableExists) {
        // Get staff statistics
        $totalStaffResult = fetchOne("SELECT COUNT(*) as total FROM staff");
        $totalStaff = $totalStaffResult ? $totalStaffResult['total'] : 0;

        $doctorsCountResult = fetchOne("SELECT COUNT(*) as total FROM staff WHERE role = 'doctor'");
        $doctorsCount = $doctorsCountResult ? $doctorsCountResult['total'] : 0;

        $nursesCountResult = fetchOne("SELECT COUNT(*) as total FROM staff WHERE role = 'nurse'");
        $nursesCount = $nursesCountResult ? $nursesCountResult['total'] : 0;
    }
} catch (Exception $e) {
    // If there's an error, we'll just use the default values
    // This allows the dashboard to load even if the database is not set up
}

// Custom header for admin dashboard
?>
<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UBTH Hospital Management System - Admin Dashboard</title>
    <!-- Tailwind CSS from CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom styles -->
    <style>
        :root {
            --primary-color: #0077cc;
            --secondary-color: #00a86b;
            --accent-color: #ff6b6b;
            --light-bg: #f0f8ff;
            --dark-bg: #1e293b;
            --dark-sidebar: #0f172a;
            --dark-card: #1e293b;
            --light-text: #f8fafc;
            --dark-text: #334155;
        }

        body {
            font-family: 'Poppins', sans-serif;
            transition: background-color 0.3s, color 0.3s;
        }

        /* Light mode (default) */
        body {
            background-color: #ffffff !important;
            color: #334155 !important;
        }

        /* Light mode specific styles */
        html.light body {
            background-color: #ffffff !important;
            color: #334155 !important;
        }

        html.light main {
            background-color: #f8fafc !important;
        }

        html.light header {
            background-color: #ffffff !important;
            border-color: #e5e7eb !important;
        }

        html.light .bg-white {
            background-color: #ffffff !important;
        }

        html.light .bg-gray-50 {
            background-color: #f8fafc !important;
        }

        /* Dark mode */
        html.dark body {
            background-color: #1e293b !important;
            color: #f8fafc !important;
        }

        html.dark .bg-white {
            background-color: #1e293b !important;
        }

        html.dark .bg-gray-50 {
            background-color: #0f172a !important;
        }

        html.dark .text-gray-900 {
            color: #f8fafc !important;
        }

        html.dark .text-gray-700 {
            color: #e2e8f0 !important;
        }

        html.dark .text-gray-600 {
            color: #cbd5e1 !important;
        }

        html.dark .text-gray-500 {
            color: #94a3b8 !important;
        }

        html.dark .border-gray-200 {
            border-color: #374151 !important;
        }

        html.dark .border-gray-300 {
            border-color: #4b5563 !important;
        }

        html.dark .bg-gray-100 {
            background-color: #374151 !important;
        }

        html.dark .bg-gray-800 {
            background-color: #1e293b !important;
        }

        html.dark .bg-gray-700 {
            background-color: #374151 !important;
        }

        /* Main content area dark mode */
        html.dark main {
            background-color: #0f172a !important;
        }

        /* Header dark mode */
        html.dark header {
            background-color: #1e293b !important;
            border-color: #374151 !important;
        }

        /* Cards and containers dark mode */
        html.dark .rounded-xl {
            background-color: #1e293b !important;
        }

        /* Input fields dark mode */
        html.dark input, html.dark select, html.dark textarea {
            background-color: #374151 !important;
            border-color: #4b5563 !important;
            color: #f8fafc !important;
        }

        html.dark input::placeholder {
            color: #9ca3af !important;
        }

        .sidebar {
            transition: all 0.3s ease;
        }

        .sidebar-item {
            transition: all 0.2s ease;
        }

        .sidebar-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar-item.active {
            background-color: rgba(255, 255, 255, 0.15);
            border-left: 4px solid var(--secondary-color);
        }

        /* Custom scrollbar for sidebar */
        .sidebar nav::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar nav::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .sidebar nav::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar nav::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Dark mode scrollbar */
        html.dark .sidebar nav::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.2);
        }

        html.dark .sidebar nav::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
        }

        .logo-container {
            display: flex;
            align-items: center;
        }

        .logo-img {
            height: 40px;
            width: auto;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes slideInUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .slide-in-up {
            animation: slideInUp 0.5s ease-out;
        }

        /* Form styles */
        .form-container {
            display: none;
        }

        .form-container.active {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }

        .form-input {
            @apply w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition;
        }

        html.dark .form-input {
            @apply bg-gray-700 border-gray-600 text-white;
        }

        .form-label {
            @apply block text-sm font-medium mb-1;
        }

        html.dark .form-label {
            @apply text-gray-200;
        }

        .btn-primary {
            @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition;
        }

        .btn-secondary {
            @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0077cc',
                        secondary: '#00a86b',
                        accent: '#ff6b6b',
                        'light-bg': '#f0f8ff'
                    }
                }
            }
        }
    </script>
</head>
<body class="transition-colors duration-300">
    <!-- Main Dashboard Container -->
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div id="sidebar" class="sidebar fixed inset-y-0 left-0 z-50 w-64 bg-gradient-to-b from-blue-600 to-green-600 dark:from-gray-800 dark:to-gray-900 transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 flex flex-col">
            <!-- Sidebar Header -->
            <div class="flex items-center justify-center h-16 px-4 border-b border-blue-500 dark:border-gray-700 flex-shrink-0">
                <div class="flex items-center space-x-3">
                    <img src="../assets/img/logo-green-removebg-preview.png" alt="UBTH Logo" class="h-8 w-auto">
                    <div class="text-white">
                        <h2 class="text-lg font-bold">UBTH</h2>
                        <p class="text-xs text-blue-200 dark:text-gray-400">Admin Panel</p>
                    </div>
                </div>
            </div>

            <!-- Navigation Menu - Scrollable -->
            <nav class="flex-1 overflow-y-auto px-4 py-4" style="scrollbar-width: thin; scrollbar-color: rgba(255,255,255,0.3) transparent;">
                <div class="space-y-2">
                    <!-- Dashboard -->
                    <a href="#" onclick="showForm('dashboard-overview')" id="nav-dashboard" class="sidebar-item active flex items-center px-4 py-3 text-white rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-200">
                        <i class="fas fa-chart-line w-5 h-5 mr-3"></i>
                        <span class="font-medium">Dashboard</span>
                    </a>

                    <!-- Patient Registration -->
                    <a href="#" onclick="showForm('patient-registration')" id="nav-patient-registration" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-200">
                        <i class="fas fa-user-plus w-5 h-5 mr-3"></i>
                        <span class="font-medium">Patient Registration</span>
                    </a>

                    <!-- Staff Registration -->
                    <a href="#" onclick="showForm('staff-registration')" id="nav-staff-registration" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-200">
                        <i class="fas fa-user-md w-5 h-5 mr-3"></i>
                        <span class="font-medium">Staff Registration</span>
                    </a>

                    <!-- Appointment Booking -->
                    <a href="#" onclick="showForm('appointment-booking')" id="nav-appointment-booking" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-200">
                        <i class="fas fa-calendar-plus w-5 h-5 mr-3"></i>
                        <span class="font-medium">Appointments</span>
                    </a>

                    <!-- Medication Entry -->
                    <a href="#" onclick="showForm('medication-entry')" id="nav-medication-entry" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-200">
                        <i class="fas fa-pills w-5 h-5 mr-3"></i>
                        <span class="font-medium">Medications</span>
                    </a>

                    <!-- Bed Assignment -->
                    <a href="#" onclick="showForm('bed-assignment')" id="nav-bed-assignment" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-200">
                        <i class="fas fa-bed w-5 h-5 mr-3"></i>
                        <span class="font-medium">Bed Assignment</span>
                    </a>

                    <!-- Emergency Room -->
                    <a href="#" onclick="showForm('emergency-room')" id="nav-emergency-room" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-200">
                        <i class="fas fa-ambulance w-5 h-5 mr-3"></i>
                        <span class="font-medium">Emergency Room</span>
                    </a>

                    <!-- ER Shift -->
                    <a href="#" onclick="showForm('er-shift')" id="nav-er-shift" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-200">
                        <i class="fas fa-clock w-5 h-5 mr-3"></i>
                        <span class="font-medium">ER Shift</span>
                    </a>

                    <!-- User Creation -->
                    <a href="#" onclick="showForm('user-creation')" id="nav-user-creation" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-200">
                        <i class="fas fa-users-cog w-5 h-5 mr-3"></i>
                        <span class="font-medium">User Creation</span>
                    </a>
                </div>

                <!-- Divider -->
                <div class="border-t border-blue-500 dark:border-gray-700 my-6"></div>

                <!-- Additional Menu Items -->
                <div class="space-y-2">
                    <!-- Reports -->
                    <a href="#" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-200">
                        <i class="fas fa-chart-bar w-5 h-5 mr-3"></i>
                        <span class="font-medium">Reports</span>
                    </a>

                    <!-- Settings -->
                    <a href="#" class="sidebar-item flex items-center px-4 py-3 text-white rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-200">
                        <i class="fas fa-cog w-5 h-5 mr-3"></i>
                        <span class="font-medium">Settings</span>
                    </a>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="flex-shrink-0 p-4 border-t border-blue-500 dark:border-gray-700">
                <div class="flex items-center space-x-3 text-white">
                    <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-sm"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium truncate"><?= htmlspecialchars($_SESSION['full_name']) ?></p>
                        <p class="text-xs text-blue-200 dark:text-gray-400">Administrator</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col overflow-hidden lg:ml-0">
            <!-- Top Navigation Bar -->
            <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between px-6 py-4">
                    <!-- Left side: Mobile menu button and Logo -->
                    <div class="flex items-center space-x-4">
                        <!-- Mobile menu button -->
                        <button id="sidebar-toggle" class="lg:hidden p-2 rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>

                        <!-- Logo and Hospital Name -->
                        <div class="flex items-center space-x-3">
                            <div class="logo-container">
                                <img src="../assets/img/logo-green-removebg-preview.png" alt="UBTH Logo" class="logo-img h-10 w-auto">
                            </div>
                            <div class="hidden md:block">
                                <h1 class="text-xl font-bold text-gray-900 dark:text-white">UBTH</h1>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Hospital Management</p>
                            </div>
                        </div>
                    </div>

                    <!-- Center: Search Bar -->
                    <div class="hidden md:flex flex-1 max-w-lg mx-8">
                        <div class="relative w-full">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                            <input type="text" id="global-search"
                                   class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="Search patients, staff, appointments...">
                        </div>
                    </div>

                    <!-- Right side: Notifications, Theme toggle, User menu -->
                    <div class="flex items-center space-x-4">
                        <!-- Mobile search button -->
                        <button class="md:hidden p-2 rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-search w-5 h-5"></i>
                        </button>

                        <!-- Notifications -->
                        <div class="relative">
                            <button class="p-2 rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 relative">
                                <i class="fas fa-bell w-5 h-5"></i>
                                <!-- Notification badge -->
                                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                            </button>
                        </div>

                        <!-- Theme toggle -->
                        <button onclick="toggleTheme()" class="p-2 rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i id="theme-icon" class="fas fa-moon w-5 h-5"></i>
                        </button>

                        <!-- User menu dropdown -->
                        <div class="relative">
                            <button id="user-menu-button" class="flex items-center space-x-3 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <!-- User avatar -->
                                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center">
                                    <span class="text-white font-medium text-sm"><?= strtoupper(substr($_SESSION['full_name'], 0, 1)) ?></span>
                                </div>
                                <!-- User info (hidden on mobile) -->
                                <div class="hidden lg:block text-left">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($_SESSION['full_name']) ?></p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Administrator</p>
                                </div>
                                <!-- Dropdown arrow -->
                                <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                            </button>

                            <!-- Dropdown menu -->
                            <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50 border border-gray-200 dark:border-gray-700">
                                <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-user mr-3"></i>
                                    Profile Settings
                                </a>
                                <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-cog mr-3"></i>
                                    System Settings
                                </a>
                                <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-question-circle mr-3"></i>
                                    Help & Support
                                </a>
                                <div class="border-t border-gray-200 dark:border-gray-700"></div>
                                <a href="../auth/logout.php" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-sign-out-alt mr-3"></i>
                                    Sign Out
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mobile search bar (hidden by default) -->
                <div id="mobile-search" class="hidden md:hidden px-6 pb-4">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text"
                               class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="Search patients, staff, appointments...">
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6">
                <!-- Dashboard Overview Section -->
                <div id="dashboard-overview" class="form-container active">
                    <!-- Welcome Section -->
                    <div class="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-xl shadow-lg p-8 mb-8 slide-in-up">
                        <div class="flex flex-col md:flex-row items-center justify-between">
                            <div>
                                <h1 class="text-3xl md:text-4xl font-bold mb-2">Welcome, <?= htmlspecialchars($_SESSION['full_name']) ?></h1>
                                <p class="text-blue-100 text-lg">Hospital Administration Dashboard</p>
                                <p class="text-blue-200 text-sm mt-1">Manage your hospital operations efficiently</p>
                            </div>
                            <div class="mt-6 md:mt-0">
                                <div class="w-32 h-32 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user-md text-6xl text-white"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Total Appointments Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 slide-in-up" style="animation-delay: 0.1s;">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Appointments</p>
                                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= number_format($totalAppointments) ?></p>
                                    <div class="flex items-center mt-2">
                                        <span class="text-sm text-gray-500 dark:text-gray-400">Today: <?= number_format($todayAppointments) ?></span>
                                    </div>
                                </div>
                                <div class="bg-blue-100 dark:bg-blue-900 p-3 rounded-full">
                                    <i class="fas fa-calendar-check text-blue-600 dark:text-blue-400 text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Total Patients Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 slide-in-up" style="animation-delay: 0.2s;">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Patients</p>
                                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= number_format($totalPatients) ?></p>
                                    <div class="flex items-center mt-2">
                                        <span class="text-sm text-gray-500 dark:text-gray-400">New: <?= number_format($newPatientsThisMonth) ?></span>
                                    </div>
                                </div>
                                <div class="bg-green-100 dark:bg-green-900 p-3 rounded-full">
                                    <i class="fas fa-user-injured text-green-600 dark:text-green-400 text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Total Staff Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 slide-in-up" style="animation-delay: 0.3s;">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Staff</p>
                                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= number_format($totalStaff) ?></p>
                                    <div class="flex items-center mt-2">
                                        <span class="text-sm text-gray-500 dark:text-gray-400">Doctors: <?= number_format($doctorsCount) ?></span>
                                    </div>
                                </div>
                                <div class="bg-purple-100 dark:bg-purple-900 p-3 rounded-full">
                                    <i class="fas fa-user-md text-purple-600 dark:text-purple-400 text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Completion Rate Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 slide-in-up" style="animation-delay: 0.4s;">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Completion Rate</p>
                                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= $totalAppointments > 0 ? round(($completedAppointments / $totalAppointments) * 100) : 0 ?>%</p>
                                    <div class="flex items-center mt-2">
                                        <span class="text-sm text-gray-500 dark:text-gray-400">Completed: <?= number_format($completedAppointments) ?></span>
                                    </div>
                                </div>
                                <div class="bg-yellow-100 dark:bg-yellow-900 p-3 rounded-full">
                                    <i class="fas fa-check-circle text-yellow-600 dark:text-yellow-400 text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity Section -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Recent Appointments -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 slide-in-up" style="animation-delay: 0.5s;">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Recent Appointments</h3>
                                <button class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                                    View All
                                </button>
                            </div>

                            <?php if (empty($recentAppointments)): ?>
                                <div class="text-center py-8">
                                    <i class="fas fa-calendar-times text-gray-400 text-4xl mb-4"></i>
                                    <p class="text-gray-500 dark:text-gray-400">No recent appointments found</p>
                                </div>
                            <?php else: ?>
                                <div class="space-y-4">
                                    <?php foreach (array_slice($recentAppointments, 0, 5) as $appointment): ?>
                                        <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                                    <i class="fas fa-user text-blue-600 dark:text-blue-400"></i>
                                                </div>
                                                <div>
                                                    <p class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($appointment['patient_name']) ?></p>
                                                    <p class="text-sm text-gray-500 dark:text-gray-400">Dr. <?= htmlspecialchars($appointment['doctor_name']) ?></p>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white"><?= date('M j', strtotime($appointment['appointment_date'])) ?></p>
                                                <p class="text-sm text-gray-500 dark:text-gray-400"><?= date('g:i A', strtotime($appointment['appointment_time'])) ?></p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Quick Actions -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 slide-in-up" style="animation-delay: 0.6s;">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Quick Actions</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <button onclick="showForm('patient-registration')" class="p-4 bg-blue-50 dark:bg-blue-900 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-lg transition-colors">
                                    <i class="fas fa-user-plus text-blue-600 dark:text-blue-400 text-2xl mb-2"></i>
                                    <p class="text-sm font-medium text-blue-900 dark:text-blue-100">Add Patient</p>
                                </button>
                                <button onclick="showForm('appointment-booking')" class="p-4 bg-green-50 dark:bg-green-900 hover:bg-green-100 dark:hover:bg-green-800 rounded-lg transition-colors">
                                    <i class="fas fa-calendar-plus text-green-600 dark:text-green-400 text-2xl mb-2"></i>
                                    <p class="text-sm font-medium text-green-900 dark:text-green-100">Book Appointment</p>
                                </button>
                                <button onclick="showForm('staff-registration')" class="p-4 bg-purple-50 dark:bg-purple-900 hover:bg-purple-100 dark:hover:bg-purple-800 rounded-lg transition-colors">
                                    <i class="fas fa-user-md text-purple-600 dark:text-purple-400 text-2xl mb-2"></i>
                                    <p class="text-sm font-medium text-purple-900 dark:text-purple-100">Add Staff</p>
                                </button>
                                <button onclick="showForm('emergency-room')" class="p-4 bg-red-50 dark:bg-red-900 hover:bg-red-100 dark:hover:bg-red-800 rounded-lg transition-colors">
                                    <i class="fas fa-ambulance text-red-600 dark:text-red-400 text-2xl mb-2"></i>
                                    <p class="text-sm font-medium text-red-900 dark:text-red-100">Emergency</p>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Patient Registration Form -->
                <div id="patient-registration" class="form-container">
                    <div class="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-xl shadow-lg p-8 mb-8">
                        <div class="flex items-center">
                            <i class="fas fa-user-plus text-4xl mr-4"></i>
                            <div>
                                <h1 class="text-3xl font-bold">Patient Registration</h1>
                                <p class="text-blue-100 mt-1">Register new patients into the hospital system</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
                        <p class="text-gray-600 dark:text-gray-300 text-center py-8">
                            <i class="fas fa-spinner fa-spin text-2xl mb-4"></i><br>
                            Loading Patient Registration Form...
                        </p>
                    </div>
                </div>

                <!-- Staff Registration Form -->
                <div id="staff-registration" class="form-container">
                    <div class="bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl shadow-lg p-8 mb-8">
                        <div class="flex items-center">
                            <i class="fas fa-user-md text-4xl mr-4"></i>
                            <div>
                                <h1 class="text-3xl font-bold">Staff Registration</h1>
                                <p class="text-purple-100 mt-1">Register new staff members into the hospital system</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
                        <p class="text-gray-600 dark:text-gray-300 text-center py-8">
                            <i class="fas fa-spinner fa-spin text-2xl mb-4"></i><br>
                            Loading Staff Registration Form...
                        </p>
                    </div>
                </div>

                <!-- Appointment Booking Form -->
                <div id="appointment-booking" class="form-container">
                    <div class="bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl shadow-lg p-8 mb-8">
                        <div class="flex items-center">
                            <i class="fas fa-calendar-plus text-4xl mr-4"></i>
                            <div>
                                <h1 class="text-3xl font-bold">Appointment Booking</h1>
                                <p class="text-green-100 mt-1">Schedule new appointments for patients</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
                        <p class="text-gray-600 dark:text-gray-300 text-center py-8">
                            <i class="fas fa-spinner fa-spin text-2xl mb-4"></i><br>
                            Loading Appointment Booking Form...
                        </p>
                    </div>
                </div>

                <!-- Medication Entry Form -->
                <div id="medication-entry" class="form-container">
                    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl shadow-lg p-8 mb-8">
                        <div class="flex items-center">
                            <i class="fas fa-pills text-4xl mr-4"></i>
                            <div>
                                <h1 class="text-3xl font-bold">Medication Entry</h1>
                                <p class="text-indigo-100 mt-1">Add new medications to the hospital inventory</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
                        <p class="text-gray-600 dark:text-gray-300 text-center py-8">
                            <i class="fas fa-spinner fa-spin text-2xl mb-4"></i><br>
                            Loading Medication Entry Form...
                        </p>
                    </div>
                </div>

                <!-- Bed Assignment Form -->
                <div id="bed-assignment" class="form-container">
                    <div class="bg-gradient-to-r from-teal-600 to-cyan-600 text-white rounded-xl shadow-lg p-8 mb-8">
                        <div class="flex items-center">
                            <i class="fas fa-bed text-4xl mr-4"></i>
                            <div>
                                <h1 class="text-3xl font-bold">Bed Assignment</h1>
                                <p class="text-teal-100 mt-1">Assign beds to patients for admission</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
                        <p class="text-gray-600 dark:text-gray-300 text-center py-8">
                            <i class="fas fa-spinner fa-spin text-2xl mb-4"></i><br>
                            Loading Bed Assignment Form...
                        </p>
                    </div>
                </div>

                <!-- Emergency Room Form -->
                <div id="emergency-room" class="form-container">
                    <div class="bg-gradient-to-r from-red-600 to-orange-600 text-white rounded-xl shadow-lg p-8 mb-8">
                        <div class="flex items-center">
                            <i class="fas fa-ambulance text-4xl mr-4"></i>
                            <div>
                                <h1 class="text-3xl font-bold">Emergency Room</h1>
                                <p class="text-red-100 mt-1">Manage emergency room patients and treatments</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
                        <p class="text-gray-600 dark:text-gray-300 text-center py-8">
                            <i class="fas fa-spinner fa-spin text-2xl mb-4"></i><br>
                            Loading Emergency Room Form...
                        </p>
                    </div>
                </div>

                <!-- ER Shift Form -->
                <div id="er-shift" class="form-container">
                    <div class="bg-gradient-to-r from-yellow-600 to-red-600 text-white rounded-xl shadow-lg p-8 mb-8">
                        <div class="flex items-center">
                            <i class="fas fa-clock text-4xl mr-4"></i>
                            <div>
                                <h1 class="text-3xl font-bold">ER Shift Management</h1>
                                <p class="text-yellow-100 mt-1">Schedule and manage emergency room shifts</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
                        <p class="text-gray-600 dark:text-gray-300 text-center py-8">
                            <i class="fas fa-spinner fa-spin text-2xl mb-4"></i><br>
                            Loading ER Shift Form...
                        </p>
                    </div>
                </div>

                <!-- User Creation Form -->
                <div id="user-creation" class="form-container">
                    <div class="bg-gradient-to-r from-cyan-600 to-blue-600 text-white rounded-xl shadow-lg p-8 mb-8">
                        <div class="flex items-center">
                            <i class="fas fa-users-cog text-4xl mr-4"></i>
                            <div>
                                <h1 class="text-3xl font-bold">User Creation</h1>
                                <p class="text-cyan-100 mt-1">Create new user accounts for hospital staff</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
                        <p class="text-gray-600 dark:text-gray-300 text-center py-8">
                            <i class="fas fa-spinner fa-spin text-2xl mb-4"></i><br>
                            Loading User Creation Form...
                        </p>
                    </div>
                </div>

            </main>
        </div>
    </div>

    <!-- JavaScript for dashboard functionality -->
    <script>
        // Global variables
        let currentTheme = localStorage.getItem('theme') || 'light';

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial theme
            applyTheme(currentTheme);
            updateThemeIcon();

            // Initialize sidebar toggle for mobile
            initializeSidebar();

            // Initialize header functionality
            initializeHeader();

            // Initialize animations
            initializeAnimations();
        });

        // Apply theme function
        function applyTheme(theme) {
            const html = document.documentElement;
            const body = document.body;

            // Remove all theme classes first
            html.classList.remove('dark', 'light');

            if (theme === 'dark') {
                html.classList.add('dark');
                body.style.backgroundColor = '#1e293b';
                body.style.color = '#f8fafc';
            } else {
                html.classList.add('light');
                body.style.backgroundColor = '#ffffff';
                body.style.color = '#334155';
            }

            currentTheme = theme;
            localStorage.setItem('theme', theme);

            console.log('Applied theme:', theme, 'HTML classes:', html.className);
        }

        // Sidebar functionality
        function initializeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('-translate-x-full');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                if (window.innerWidth < 1024) {
                    if (!sidebar.contains(event.target) && !sidebarToggle?.contains(event.target)) {
                        sidebar.classList.add('-translate-x-full');
                    }
                }
            });
        }

        // Header functionality
        function initializeHeader() {
            // User menu dropdown
            const userMenuButton = document.getElementById('user-menu-button');
            const userMenu = document.getElementById('user-menu');

            if (userMenuButton && userMenu) {
                userMenuButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    userMenu.classList.toggle('hidden');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!userMenuButton.contains(e.target) && !userMenu.contains(e.target)) {
                        userMenu.classList.add('hidden');
                    }
                });
            }

            // Mobile search toggle
            const mobileSearchButton = document.querySelector('.md\\:hidden .fas.fa-search').parentElement;
            const mobileSearch = document.getElementById('mobile-search');

            if (mobileSearchButton && mobileSearch) {
                mobileSearchButton.addEventListener('click', function() {
                    mobileSearch.classList.toggle('hidden');
                });
            }

            // Global search functionality
            const globalSearch = document.getElementById('global-search');
            if (globalSearch) {
                globalSearch.addEventListener('input', function(e) {
                    const searchTerm = e.target.value.toLowerCase();
                    // Implement search functionality here
                    console.log('Searching for:', searchTerm);
                });
            }
        }

        // Animation initialization
        function initializeAnimations() {
            // Add staggered animations to cards
            const cards = document.querySelectorAll('.slide-in-up');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });
        }

        // Form switching functionality
        function showForm(formId) {
            // Hide all form containers
            const forms = document.querySelectorAll('.form-container');
            forms.forEach(form => {
                form.classList.remove('active');
            });

            // Show selected form
            const targetForm = document.getElementById(formId);
            if (targetForm) {
                targetForm.classList.add('active');

                // Load form content if it's a placeholder
                loadFormContent(formId);
            }

            // Update sidebar active state
            updateSidebarActive(formId);

            // Close mobile sidebar if open
            if (window.innerWidth < 1024) {
                const sidebar = document.getElementById('sidebar');
                sidebar.classList.add('-translate-x-full');
            }
        }

        // Load form content dynamically
        function loadFormContent(formId) {
            const formContainer = document.getElementById(formId);
            const contentDiv = formContainer.querySelector('.bg-white');

            // Check if content is already loaded (not a spinner)
            if (!contentDiv.innerHTML.includes('fa-spinner')) {
                return;
            }

            // Show loading state
            contentDiv.innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-2xl mb-4 text-blue-600"></i><br>
                    <span class="text-gray-600 dark:text-gray-300">Loading form...</span>
                </div>
            `;

            // Simulate loading delay and then show form placeholder
            setTimeout(() => {
                let formContent = '';

                switch(formId) {
                    case 'patient-registration':
                        formContent = getPatientRegistrationForm();
                        break;
                    case 'staff-registration':
                        formContent = getStaffRegistrationForm();
                        break;
                    case 'appointment-booking':
                        formContent = getAppointmentBookingForm();
                        break;
                    case 'medication-entry':
                        formContent = getMedicationEntryForm();
                        break;
                    case 'bed-assignment':
                        formContent = getBedAssignmentForm();
                        break;
                    case 'emergency-room':
                        formContent = getEmergencyRoomForm();
                        break;
                    case 'er-shift':
                        formContent = getERShiftForm();
                        break;
                    case 'user-creation':
                        formContent = getUserCreationForm();
                        break;
                    default:
                        formContent = '<p class="text-center py-8 text-gray-500">Form not found</p>';
                }

                contentDiv.innerHTML = formContent;
            }, 500);
        }

        // Update sidebar active state
        function updateSidebarActive(formId) {
            // Remove active class from all sidebar items
            const sidebarItems = document.querySelectorAll('.sidebar-item');
            sidebarItems.forEach(item => {
                item.classList.remove('active');
            });

            // Add active class to current item
            let activeNavId = '';
            switch(formId) {
                case 'dashboard-overview':
                    activeNavId = 'nav-dashboard';
                    break;
                case 'patient-registration':
                    activeNavId = 'nav-patient-registration';
                    break;
                case 'staff-registration':
                    activeNavId = 'nav-staff-registration';
                    break;
                case 'appointment-booking':
                    activeNavId = 'nav-appointment-booking';
                    break;
                case 'medication-entry':
                    activeNavId = 'nav-medication-entry';
                    break;
                case 'bed-assignment':
                    activeNavId = 'nav-bed-assignment';
                    break;
                case 'emergency-room':
                    activeNavId = 'nav-emergency-room';
                    break;
                case 'er-shift':
                    activeNavId = 'nav-er-shift';
                    break;
                case 'user-creation':
                    activeNavId = 'nav-user-creation';
                    break;
            }

            if (activeNavId) {
                const activeNav = document.getElementById(activeNavId);
                if (activeNav) {
                    activeNav.classList.add('active');
                }
            }
        }

        // Theme toggle functionality
        function toggleTheme() {
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            applyTheme(newTheme);
            updateThemeIcon();

            console.log('Theme toggled to:', newTheme); // Debug log
        }

        // Update theme icon
        function updateThemeIcon() {
            const themeIcon = document.getElementById('theme-icon');
            if (themeIcon) {
                themeIcon.className = currentTheme === 'light' ? 'fas fa-moon w-5 h-5' : 'fas fa-sun w-5 h-5';
            }
        }

        // Form content functions (simplified versions for demo)
        function getPatientRegistrationForm() {
            return `
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Full Name *</label>
                            <input type="text" class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" placeholder="Enter patient's full name" required>
                        </div>
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Email Address *</label>
                            <input type="email" class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" placeholder="<EMAIL>" required>
                        </div>
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Phone Number *</label>
                            <input type="tel" class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" placeholder="+234 xxx xxx xxxx" required>
                        </div>
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Date of Birth *</label>
                            <input type="date" class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" required>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" class="btn-primary px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg">
                            <i class="fas fa-save mr-2"></i>Register Patient
                        </button>
                    </div>
                </form>
            `;
        }

        function getStaffRegistrationForm() {
            return `
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Full Name *</label>
                            <input type="text" class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" placeholder="Enter staff member's full name" required>
                        </div>
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Role *</label>
                            <select class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" required>
                                <option value="">Select Role</option>
                                <option value="doctor">Doctor</option>
                                <option value="nurse">Nurse</option>
                                <option value="technician">Technician</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" class="btn-primary px-8 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg">
                            <i class="fas fa-save mr-2"></i>Register Staff
                        </button>
                    </div>
                </form>
            `;
        }

        function getAppointmentBookingForm() {
            return `
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Patient *</label>
                            <select class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" required>
                                <option value="">Select Patient</option>
                                <option value="1">John Doe</option>
                                <option value="2">Jane Smith</option>
                            </select>
                        </div>
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Doctor *</label>
                            <select class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" required>
                                <option value="">Select Doctor</option>
                                <option value="1">Dr. Johnson</option>
                                <option value="2">Dr. Williams</option>
                            </select>
                        </div>
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Appointment Date *</label>
                            <input type="date" class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" required>
                        </div>
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Appointment Time *</label>
                            <input type="time" class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" required>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" class="btn-primary px-8 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg">
                            <i class="fas fa-calendar-check mr-2"></i>Book Appointment
                        </button>
                    </div>
                </form>
            `;
        }

        function getMedicationEntryForm() {
            return `
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Medication Name *</label>
                            <input type="text" class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" placeholder="Enter medication name" required>
                        </div>
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Dosage Form *</label>
                            <select class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" required>
                                <option value="">Select Dosage Form</option>
                                <option value="tablet">Tablet</option>
                                <option value="capsule">Capsule</option>
                                <option value="syrup">Syrup</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" class="btn-primary px-8 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg">
                            <i class="fas fa-save mr-2"></i>Add Medication
                        </button>
                    </div>
                </form>
            `;
        }

        function getBedAssignmentForm() {
            return `
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Patient *</label>
                            <select class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" required>
                                <option value="">Select Patient</option>
                                <option value="1">John Doe</option>
                                <option value="2">Jane Smith</option>
                            </select>
                        </div>
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Room Number *</label>
                            <input type="text" class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" placeholder="e.g., 101, A-205" required>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" class="btn-primary px-8 py-3 bg-teal-600 hover:bg-teal-700 text-white rounded-lg">
                            <i class="fas fa-bed mr-2"></i>Assign Bed
                        </button>
                    </div>
                </form>
            `;
        }

        function getEmergencyRoomForm() {
            return `
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Patient *</label>
                            <select class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" required>
                                <option value="">Select Patient</option>
                                <option value="1">John Doe</option>
                                <option value="2">Jane Smith</option>
                            </select>
                        </div>
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Triage Level *</label>
                            <select class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" required>
                                <option value="">Select Triage Level</option>
                                <option value="1">Level 1 - Immediate</option>
                                <option value="2">Level 2 - Urgent</option>
                                <option value="3">Level 3 - Less Urgent</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" class="btn-primary px-8 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg">
                            <i class="fas fa-save mr-2"></i>Save Emergency Record
                        </button>
                    </div>
                </form>
            `;
        }

        function getERShiftForm() {
            return `
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Staff Member *</label>
                            <select class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" required>
                                <option value="">Select Staff Member</option>
                                <option value="1">Dr. Johnson</option>
                                <option value="2">Nurse Williams</option>
                            </select>
                        </div>
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Shift Type *</label>
                            <select class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" required>
                                <option value="">Select Shift Type</option>
                                <option value="day">Day Shift</option>
                                <option value="evening">Evening Shift</option>
                                <option value="night">Night Shift</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" class="btn-primary px-8 py-3 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg">
                            <i class="fas fa-save mr-2"></i>Schedule Shift
                        </button>
                    </div>
                </form>
            `;
        }

        function getUserCreationForm() {
            return `
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Username *</label>
                            <input type="text" class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" placeholder="Enter username" required>
                        </div>
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Email Address *</label>
                            <input type="email" class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" placeholder="<EMAIL>" required>
                        </div>
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Role *</label>
                            <select class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" required>
                                <option value="">Select Role</option>
                                <option value="admin">Administrator</option>
                                <option value="doctor">Doctor</option>
                                <option value="nurse">Nurse</option>
                            </select>
                        </div>
                        <div>
                            <label class="form-label text-gray-700 dark:text-gray-300">Password *</label>
                            <input type="password" class="form-input border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" placeholder="Enter password" required>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" class="btn-primary px-8 py-3 bg-cyan-600 hover:bg-cyan-700 text-white rounded-lg">
                            <i class="fas fa-user-plus mr-2"></i>Create User
                        </button>
                    </div>
                </form>
            `;
        }
    </script>
</body>
</html>
