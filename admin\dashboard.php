<?php
/**
 * Admin Dashboard
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require admin login
requireLogin('../auth/admin_login.php');
requireRole('admin', '../auth/admin_login.php');

// Get admin information
$userId = $_SESSION['user_id'];
$staff = null;

// Only try to fetch from database if not using default admin
if ($userId != 1 || $_SESSION['username'] != 'ubthadmin') {
    $staff = fetchOne("SELECT * FROM staff WHERE user_id = ?", "i", [$userId]);
}

// Initialize variables with default values
$recentAppointments = [];
$totalAppointments = 0;
$todayAppointments = 0;
$pendingAppointments = 0;
$completedAppointments = 0;
$totalPatients = 0;
$newPatientsThisMonth = 0;
$totalStaff = 0;
$doctorsCount = 0;
$nursesCount = 0;

// Only try to fetch data if not using default admin or if database is available
try {
    // Check if the appointment table exists
    $tableExists = fetchOne("SHOW TABLES LIKE 'appointment'");

    if ($tableExists) {
        // Get recent appointments
        $recentAppointments = fetchAll(
            "SELECT a.*, p.full_name as patient_name, s.full_name as doctor_name, s.specialty
             FROM appointment a
             JOIN patient p ON a.patient_id = p.id
             JOIN staff s ON a.staff_id = s.id
             ORDER BY a.appointment_date DESC, a.appointment_time DESC
             LIMIT 10"
        ) ?: [];

        // Get appointment statistics
        $totalAppointmentsResult = fetchOne("SELECT COUNT(*) as total FROM appointment");
        $totalAppointments = $totalAppointmentsResult ? $totalAppointmentsResult['total'] : 0;

        $todayAppointmentsResult = fetchOne("SELECT COUNT(*) as total FROM appointment WHERE appointment_date = CURDATE()");
        $todayAppointments = $todayAppointmentsResult ? $todayAppointmentsResult['total'] : 0;

        $pendingAppointmentsResult = fetchOne("SELECT COUNT(*) as total FROM appointment WHERE status = 'scheduled'");
        $pendingAppointments = $pendingAppointmentsResult ? $pendingAppointmentsResult['total'] : 0;

        $completedAppointmentsResult = fetchOne("SELECT COUNT(*) as total FROM appointment WHERE status = 'completed'");
        $completedAppointments = $completedAppointmentsResult ? $completedAppointmentsResult['total'] : 0;
    }

    // Check if the patient table exists
    $tableExists = fetchOne("SHOW TABLES LIKE 'patient'");

    if ($tableExists) {
        // Get patient statistics
        $totalPatientsResult = fetchOne("SELECT COUNT(*) as total FROM patient");
        $totalPatients = $totalPatientsResult ? $totalPatientsResult['total'] : 0;

        $newPatientsThisMonthResult = fetchOne(
            "SELECT COUNT(*) as total FROM patient
             WHERE MONTH(created_at) = MONTH(CURRENT_DATE())
             AND YEAR(created_at) = YEAR(CURRENT_DATE())"
        );
        $newPatientsThisMonth = $newPatientsThisMonthResult ? $newPatientsThisMonthResult['total'] : 0;
    }

    // Check if the staff table exists
    $tableExists = fetchOne("SHOW TABLES LIKE 'staff'");

    if ($tableExists) {
        // Get staff statistics
        $totalStaffResult = fetchOne("SELECT COUNT(*) as total FROM staff");
        $totalStaff = $totalStaffResult ? $totalStaffResult['total'] : 0;

        $doctorsCountResult = fetchOne("SELECT COUNT(*) as total FROM staff WHERE role = 'doctor'");
        $doctorsCount = $doctorsCountResult ? $doctorsCountResult['total'] : 0;

        $nursesCountResult = fetchOne("SELECT COUNT(*) as total FROM staff WHERE role = 'nurse'");
        $nursesCount = $nursesCountResult ? $nursesCountResult['total'] : 0;
    }
} catch (Exception $e) {
    // If there's an error, we'll just use the default values
    // This allows the dashboard to load even if the database is not set up
}

// Include header
include_once '../includes/header.php';
?>

<div class="bg-gradient-to-r from-primary to-secondary text-white rounded-lg shadow-md p-8 mb-6 slide-in-up">
    <div class="flex flex-col md:flex-row items-center justify-between">
        <div>
            <h1 class="text-2xl md:text-3xl font-bold mb-2">Welcome, <?= htmlspecialchars($_SESSION['full_name']) ?></h1>
            <p class="text-white text-opacity-90">Hospital Administration Dashboard</p>
        </div>
        <div class="mt-4 md:mt-0">
            <img src="https://images.unsplash.com/photo-1551076805-e1869033e561?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1332&q=80"
                 alt="Hospital Admin" class="w-32 h-32 object-cover rounded-full border-4 border-white">
        </div>
    </div>
</div>

<!-- Statistics Overview -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Appointments Stats -->
    <div class="bg-white rounded-lg shadow-md p-6 slide-in-up" style="animation-delay: 0.1s;">
        <div class="flex items-center mb-4">
            <div class="bg-primary bg-opacity-10 p-3 rounded-full mr-4">
                <i class="fas fa-calendar-check text-primary text-xl"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-800"><?= number_format($totalAppointments) ?></h3>
                <p class="text-gray-500 text-sm">Total Appointments</p>
            </div>
        </div>
        <div class="flex justify-between text-sm">
            <div>
                <span class="text-gray-500">Today:</span>
                <span class="font-medium"><?= number_format($todayAppointments) ?></span>
            </div>
            <div>
                <span class="text-gray-500">Pending:</span>
                <span class="font-medium"><?= number_format($pendingAppointments) ?></span>
            </div>
        </div>
    </div>

    <!-- Patients Stats -->
    <div class="bg-white rounded-lg shadow-md p-6 slide-in-up" style="animation-delay: 0.2s;">
        <div class="flex items-center mb-4">
            <div class="bg-secondary bg-opacity-10 p-3 rounded-full mr-4">
                <i class="fas fa-user-injured text-secondary text-xl"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-800"><?= number_format($totalPatients) ?></h3>
                <p class="text-gray-500 text-sm">Total Patients</p>
            </div>
        </div>
        <div class="flex justify-between text-sm">
            <div>
                <span class="text-gray-500">New this month:</span>
                <span class="font-medium"><?= number_format($newPatientsThisMonth) ?></span>
            </div>
        </div>
    </div>

    <!-- Staff Stats -->
    <div class="bg-white rounded-lg shadow-md p-6 slide-in-up" style="animation-delay: 0.3s;">
        <div class="flex items-center mb-4">
            <div class="bg-primary bg-opacity-10 p-3 rounded-full mr-4">
                <i class="fas fa-user-md text-primary text-xl"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-800"><?= number_format($totalStaff) ?></h3>
                <p class="text-gray-500 text-sm">Total Staff</p>
            </div>
        </div>
        <div class="flex justify-between text-sm">
            <div>
                <span class="text-gray-500">Doctors:</span>
                <span class="font-medium"><?= number_format($doctorsCount) ?></span>
            </div>
            <div>
                <span class="text-gray-500">Nurses:</span>
                <span class="font-medium"><?= number_format($nursesCount) ?></span>
            </div>
        </div>
    </div>

    <!-- Completed Appointments -->
    <div class="bg-white rounded-lg shadow-md p-6 slide-in-up" style="animation-delay: 0.4s;">
        <div class="flex items-center mb-4">
            <div class="bg-secondary bg-opacity-10 p-3 rounded-full mr-4">
                <i class="fas fa-check-circle text-secondary text-xl"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-800"><?= number_format($completedAppointments) ?></h3>
                <p class="text-gray-500 text-sm">Completed Appointments</p>
            </div>
        </div>
        <div class="flex justify-between text-sm">
            <div>
                <span class="text-gray-500">Completion Rate:</span>
                <span class="font-medium"><?= $totalAppointments > 0 ? round(($completedAppointments / $totalAppointments) * 100) : 0 ?>%</span>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions and Recent Appointments -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow-md p-6 slide-in-up" style="animation-delay: 0.5s;">
        <div class="flex items-center mb-4">
            <div class="bg-primary bg-opacity-10 p-2 rounded-full mr-3">
                <i class="fas fa-bolt text-primary text-xl"></i>
            </div>
            <h2 class="text-xl font-semibold text-gray-800">Quick Actions</h2>
        </div>
        <div class="space-y-3">
            <a href="appointments.php" class="flex items-center p-4 bg-primary bg-opacity-10 text-primary rounded-lg hover:bg-opacity-20 transition">
                <i class="fas fa-calendar-alt text-xl mr-3"></i>
                <div>
                    <div class="font-medium">Manage Appointments</div>
                    <div class="text-sm text-gray-600">View and update appointment status</div>
                </div>
            </a>
            <a href="patients.php" class="flex items-center p-4 bg-secondary bg-opacity-10 text-secondary rounded-lg hover:bg-opacity-20 transition">
                <i class="fas fa-users text-xl mr-3"></i>
                <div>
                    <div class="font-medium">Manage Patients</div>
                    <div class="text-sm text-gray-600">View and update patient records</div>
                </div>
            </a>
            <a href="staff.php" class="flex items-center p-4 bg-primary bg-opacity-10 text-primary rounded-lg hover:bg-opacity-20 transition">
                <i class="fas fa-user-md text-xl mr-3"></i>
                <div>
                    <div class="font-medium">Manage Staff</div>
                    <div class="text-sm text-gray-600">View and update staff information</div>
                </div>
            </a>
            <a href="reports.php" class="flex items-center p-4 bg-secondary bg-opacity-10 text-secondary rounded-lg hover:bg-opacity-20 transition">
                <i class="fas fa-chart-bar text-xl mr-3"></i>
                <div>
                    <div class="font-medium">Generate Reports</div>
                    <div class="text-sm text-gray-600">Create and view hospital reports</div>
                </div>
            </a>
        </div>
    </div>

    <!-- Recent Appointments -->
    <div class="md:col-span-2 bg-white rounded-lg shadow-md p-6 slide-in-up" style="animation-delay: 0.6s;">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="bg-secondary bg-opacity-10 p-2 rounded-full mr-3">
                    <i class="fas fa-calendar-alt text-secondary text-xl"></i>
                </div>
                <h2 class="text-xl font-semibold text-gray-800">Recent Appointments</h2>
            </div>
            <a href="appointments.php" class="text-secondary hover:text-secondary-dark transition">
                View All
            </a>
        </div>

        <?php if (empty($recentAppointments)): ?>
            <div class="bg-gray-50 p-6 rounded-lg text-center">
                <img src="https://images.unsplash.com/photo-1579684385127-1ef15d508118?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=880&q=80"
                     alt="No appointments" class="w-32 h-32 object-cover rounded-full mx-auto mb-4">
                <p class="text-gray-500 italic">No appointments found.</p>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Doctor</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($recentAppointments as $appointment): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="font-medium text-gray-900"><?= htmlspecialchars($appointment['patient_name']) ?></div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="font-medium text-gray-900">Dr. <?= htmlspecialchars($appointment['doctor_name']) ?></div>
                                    <div class="text-sm text-gray-500"><?= htmlspecialchars($appointment['specialty']) ?></div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?= date('M j, Y', strtotime($appointment['appointment_date'])) ?></div>
                                    <div class="text-sm text-gray-500"><?= date('g:i A', strtotime($appointment['appointment_time'])) ?></div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <?php
                                    $statusClass = '';
                                    switch ($appointment['status']) {
                                        case 'scheduled':
                                            $statusClass = 'bg-blue-100 text-blue-800';
                                            break;
                                        case 'completed':
                                            $statusClass = 'bg-green-100 text-green-800';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'bg-red-100 text-red-800';
                                            break;
                                        default:
                                            $statusClass = 'bg-gray-100 text-gray-800';
                                    }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?= $statusClass ?>">
                                        <?= ucfirst(htmlspecialchars($appointment['status'])) ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
