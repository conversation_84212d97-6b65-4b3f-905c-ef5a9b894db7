<?php
/**
 * Admin Dashboard
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require admin login
requireLogin('../auth/admin_login.php');
requireRole('admin', '../auth/admin_login.php');

// Get admin information
$userId = $_SESSION['user_id'];
$staff = null;

// Only try to fetch from database if not using default admin
if ($userId != 1 || $_SESSION['username'] != 'ubthadmin') {
    $staff = fetchOne("SELECT * FROM staff WHERE user_id = ?", "i", [$userId]);
}

// Initialize variables with default values
$recentAppointments = [];
$totalAppointments = 0;
$todayAppointments = 0;
$pendingAppointments = 0;
$completedAppointments = 0;
$totalPatients = 0;
$newPatientsThisMonth = 0;
$totalStaff = 0;
$doctorsCount = 0;
$nursesCount = 0;

// Only try to fetch data if not using default admin or if database is available
try {
    // Check if the appointment table exists
    $tableExists = fetchOne("SHOW TABLES LIKE 'appointment'");

    if ($tableExists) {
        // Get recent appointments
        $recentAppointments = fetchAll(
            "SELECT a.*, p.full_name as patient_name, s.full_name as doctor_name, s.specialty
             FROM appointment a
             JOIN patient p ON a.patient_id = p.id
             JOIN staff s ON a.staff_id = s.id
             ORDER BY a.appointment_date DESC, a.appointment_time DESC
             LIMIT 10"
        ) ?: [];

        // Get appointment statistics
        $totalAppointmentsResult = fetchOne("SELECT COUNT(*) as total FROM appointment");
        $totalAppointments = $totalAppointmentsResult ? $totalAppointmentsResult['total'] : 0;

        $todayAppointmentsResult = fetchOne("SELECT COUNT(*) as total FROM appointment WHERE appointment_date = CURDATE()");
        $todayAppointments = $todayAppointmentsResult ? $todayAppointmentsResult['total'] : 0;

        $pendingAppointmentsResult = fetchOne("SELECT COUNT(*) as total FROM appointment WHERE status = 'scheduled'");
        $pendingAppointments = $pendingAppointmentsResult ? $pendingAppointmentsResult['total'] : 0;

        $completedAppointmentsResult = fetchOne("SELECT COUNT(*) as total FROM appointment WHERE status = 'completed'");
        $completedAppointments = $completedAppointmentsResult ? $completedAppointmentsResult['total'] : 0;
    }

    // Check if the patient table exists
    $tableExists = fetchOne("SHOW TABLES LIKE 'patient'");

    if ($tableExists) {
        // Get patient statistics
        $totalPatientsResult = fetchOne("SELECT COUNT(*) as total FROM patient");
        $totalPatients = $totalPatientsResult ? $totalPatientsResult['total'] : 0;

        $newPatientsThisMonthResult = fetchOne(
            "SELECT COUNT(*) as total FROM patient
             WHERE MONTH(created_at) = MONTH(CURRENT_DATE())
             AND YEAR(created_at) = YEAR(CURRENT_DATE())"
        );
        $newPatientsThisMonth = $newPatientsThisMonthResult ? $newPatientsThisMonthResult['total'] : 0;
    }

    // Check if the staff table exists
    $tableExists = fetchOne("SHOW TABLES LIKE 'staff'");

    if ($tableExists) {
        // Get staff statistics
        $totalStaffResult = fetchOne("SELECT COUNT(*) as total FROM staff");
        $totalStaff = $totalStaffResult ? $totalStaffResult['total'] : 0;

        $doctorsCountResult = fetchOne("SELECT COUNT(*) as total FROM staff WHERE role = 'doctor'");
        $doctorsCount = $doctorsCountResult ? $doctorsCountResult['total'] : 0;

        $nursesCountResult = fetchOne("SELECT COUNT(*) as total FROM staff WHERE role = 'nurse'");
        $nursesCount = $nursesCountResult ? $nursesCountResult['total'] : 0;
    }
} catch (Exception $e) {
    // If there's an error, we'll just use the default values
    // This allows the dashboard to load even if the database is not set up
}

// Custom header for admin dashboard
?>
<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UBTH Hospital Management System - Admin Dashboard</title>
    <!-- Tailwind CSS from CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom styles -->
    <style>
        :root {
            --primary-color: #0077cc;
            --secondary-color: #00a86b;
            --accent-color: #ff6b6b;
            --light-bg: #f0f8ff;
            --dark-bg: #1e293b;
            --dark-sidebar: #0f172a;
            --dark-card: #1e293b;
            --light-text: #f8fafc;
            --dark-text: #334155;
        }

        body {
            font-family: 'Poppins', sans-serif;
            transition: background-color 0.3s, color 0.3s;
        }

        /* Light mode */
        html.light body {
            background-color: var(--light-bg);
            color: var(--dark-text);
        }

        /* Dark mode */
        html.dark body {
            background-color: var(--dark-bg);
            color: var(--light-text);
        }

        html.dark .dark\:bg-dark-card {
            background-color: var(--dark-card);
        }

        html.dark .dark\:text-light {
            color: var(--light-text);
        }

        html.dark .dark\:border-gray-700 {
            border-color: #374151;
        }

        .sidebar {
            transition: all 0.3s ease;
        }

        .sidebar-item {
            transition: all 0.2s ease;
        }

        .sidebar-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar-item.active {
            border-left: 4px solid var(--secondary-color);
        }

        .logo-container {
            display: flex;
            align-items: center;
        }

        .logo-img {
            height: 40px;
            width: auto;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes slideInUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .slide-in-up {
            animation: slideInUp 0.5s ease-out;
        }

        /* Form styles */
        .form-container {
            display: none;
        }

        .form-container.active {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }

        .form-input {
            @apply w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition;
        }

        html.dark .form-input {
            @apply bg-gray-700 border-gray-600 text-white;
        }

        .form-label {
            @apply block text-sm font-medium mb-1;
        }

        html.dark .form-label {
            @apply text-gray-200;
        }

        .btn-primary {
            @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition;
        }

        .btn-secondary {
            @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0077cc',
                        secondary: '#00a86b',
                        accent: '#ff6b6b',
                        'light-bg': '#f0f8ff'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <!-- Main Dashboard Container -->
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div id="sidebar" class="sidebar fixed inset-y-0 left-0 z-50 w-64 bg-gradient-to-b from-blue-600 to-green-600 dark:from-gray-800 dark:to-gray-900 transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0">
            <!-- Sidebar content will be added in Section 3 -->
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col overflow-hidden lg:ml-0">
            <!-- Top Navigation Bar -->
            <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
                <!-- Header content will be added in Section 2 -->
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6">
                <!-- Dashboard Overview Section -->
                <div id="dashboard-overview" class="form-container active">
                    <!-- Welcome Section -->
                    <div class="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-xl shadow-lg p-8 mb-8 slide-in-up">
                        <div class="flex flex-col md:flex-row items-center justify-between">
                            <div>
                                <h1 class="text-3xl md:text-4xl font-bold mb-2">Welcome, <?= htmlspecialchars($_SESSION['full_name']) ?></h1>
                                <p class="text-blue-100 text-lg">Hospital Administration Dashboard</p>
                                <p class="text-blue-200 text-sm mt-1">Manage your hospital operations efficiently</p>
                            </div>
                            <div class="mt-6 md:mt-0">
                                <div class="w-32 h-32 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user-md text-6xl text-white"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Total Appointments Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 slide-in-up" style="animation-delay: 0.1s;">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Appointments</p>
                                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= number_format($totalAppointments) ?></p>
                                    <div class="flex items-center mt-2">
                                        <span class="text-sm text-gray-500 dark:text-gray-400">Today: <?= number_format($todayAppointments) ?></span>
                                    </div>
                                </div>
                                <div class="bg-blue-100 dark:bg-blue-900 p-3 rounded-full">
                                    <i class="fas fa-calendar-check text-blue-600 dark:text-blue-400 text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Total Patients Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 slide-in-up" style="animation-delay: 0.2s;">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Patients</p>
                                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= number_format($totalPatients) ?></p>
                                    <div class="flex items-center mt-2">
                                        <span class="text-sm text-gray-500 dark:text-gray-400">New: <?= number_format($newPatientsThisMonth) ?></span>
                                    </div>
                                </div>
                                <div class="bg-green-100 dark:bg-green-900 p-3 rounded-full">
                                    <i class="fas fa-user-injured text-green-600 dark:text-green-400 text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Total Staff Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 slide-in-up" style="animation-delay: 0.3s;">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Staff</p>
                                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= number_format($totalStaff) ?></p>
                                    <div class="flex items-center mt-2">
                                        <span class="text-sm text-gray-500 dark:text-gray-400">Doctors: <?= number_format($doctorsCount) ?></span>
                                    </div>
                                </div>
                                <div class="bg-purple-100 dark:bg-purple-900 p-3 rounded-full">
                                    <i class="fas fa-user-md text-purple-600 dark:text-purple-400 text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Completion Rate Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 slide-in-up" style="animation-delay: 0.4s;">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Completion Rate</p>
                                    <p class="text-3xl font-bold text-gray-900 dark:text-white"><?= $totalAppointments > 0 ? round(($completedAppointments / $totalAppointments) * 100) : 0 ?>%</p>
                                    <div class="flex items-center mt-2">
                                        <span class="text-sm text-gray-500 dark:text-gray-400">Completed: <?= number_format($completedAppointments) ?></span>
                                    </div>
                                </div>
                                <div class="bg-yellow-100 dark:bg-yellow-900 p-3 rounded-full">
                                    <i class="fas fa-check-circle text-yellow-600 dark:text-yellow-400 text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity Section -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Recent Appointments -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 slide-in-up" style="animation-delay: 0.5s;">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Recent Appointments</h3>
                                <button class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                                    View All
                                </button>
                            </div>

                            <?php if (empty($recentAppointments)): ?>
                                <div class="text-center py-8">
                                    <i class="fas fa-calendar-times text-gray-400 text-4xl mb-4"></i>
                                    <p class="text-gray-500 dark:text-gray-400">No recent appointments found</p>
                                </div>
                            <?php else: ?>
                                <div class="space-y-4">
                                    <?php foreach (array_slice($recentAppointments, 0, 5) as $appointment): ?>
                                        <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                                    <i class="fas fa-user text-blue-600 dark:text-blue-400"></i>
                                                </div>
                                                <div>
                                                    <p class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($appointment['patient_name']) ?></p>
                                                    <p class="text-sm text-gray-500 dark:text-gray-400">Dr. <?= htmlspecialchars($appointment['doctor_name']) ?></p>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white"><?= date('M j', strtotime($appointment['appointment_date'])) ?></p>
                                                <p class="text-sm text-gray-500 dark:text-gray-400"><?= date('g:i A', strtotime($appointment['appointment_time'])) ?></p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Quick Actions -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 slide-in-up" style="animation-delay: 0.6s;">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Quick Actions</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <button onclick="showForm('patient-registration')" class="p-4 bg-blue-50 dark:bg-blue-900 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-lg transition-colors">
                                    <i class="fas fa-user-plus text-blue-600 dark:text-blue-400 text-2xl mb-2"></i>
                                    <p class="text-sm font-medium text-blue-900 dark:text-blue-100">Add Patient</p>
                                </button>
                                <button onclick="showForm('appointment-booking')" class="p-4 bg-green-50 dark:bg-green-900 hover:bg-green-100 dark:hover:bg-green-800 rounded-lg transition-colors">
                                    <i class="fas fa-calendar-plus text-green-600 dark:text-green-400 text-2xl mb-2"></i>
                                    <p class="text-sm font-medium text-green-900 dark:text-green-100">Book Appointment</p>
                                </button>
                                <button onclick="showForm('staff-registration')" class="p-4 bg-purple-50 dark:bg-purple-900 hover:bg-purple-100 dark:hover:bg-purple-800 rounded-lg transition-colors">
                                    <i class="fas fa-user-md text-purple-600 dark:text-purple-400 text-2xl mb-2"></i>
                                    <p class="text-sm font-medium text-purple-900 dark:text-purple-100">Add Staff</p>
                                </button>
                                <button onclick="showForm('emergency-room')" class="p-4 bg-red-50 dark:bg-red-900 hover:bg-red-100 dark:hover:bg-red-800 rounded-lg transition-colors">
                                    <i class="fas fa-ambulance text-red-600 dark:text-red-400 text-2xl mb-2"></i>
                                    <p class="text-sm font-medium text-red-900 dark:text-red-100">Emergency</p>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form containers will be added in Section 4 -->

            </main>
        </div>
    </div>

    <!-- JavaScript for dashboard functionality -->
    <script>
        // Global variables
        let currentTheme = localStorage.getItem('theme') || 'light';

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial theme
            document.documentElement.className = currentTheme;

            // Initialize sidebar toggle for mobile
            initializeSidebar();

            // Initialize animations
            initializeAnimations();
        });

        // Sidebar functionality
        function initializeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('-translate-x-full');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                if (window.innerWidth < 1024) {
                    if (!sidebar.contains(event.target) && !sidebarToggle?.contains(event.target)) {
                        sidebar.classList.add('-translate-x-full');
                    }
                }
            });
        }

        // Animation initialization
        function initializeAnimations() {
            // Add staggered animations to cards
            const cards = document.querySelectorAll('.slide-in-up');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });
        }

        // Form switching functionality (will be expanded in Section 4)
        function showForm(formId) {
            // Hide all form containers
            const forms = document.querySelectorAll('.form-container');
            forms.forEach(form => {
                form.classList.remove('active');
            });

            // Show selected form
            const targetForm = document.getElementById(formId);
            if (targetForm) {
                targetForm.classList.add('active');
            }

            // Update sidebar active state (will be implemented in Section 3)
            updateSidebarActive(formId);
        }

        // Placeholder for sidebar active state update
        function updateSidebarActive(formId) {
            // Will be implemented in Section 3
        }

        // Theme toggle functionality (will be expanded in Section 6)
        function toggleTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.className = currentTheme;
            localStorage.setItem('theme', currentTheme);

            // Update theme toggle button icon
            const themeIcon = document.getElementById('theme-icon');
            if (themeIcon) {
                themeIcon.className = currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
            }
        }
    </script>
</body>
</html>
