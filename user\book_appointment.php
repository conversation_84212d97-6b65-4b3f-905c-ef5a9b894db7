<?php
/**
 * Book Appointment Page
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require patient login
requireLogin('../auth/user_login.php');
requireRole('patient', '../auth/user_login.php');

// Get patient information
$userId = $_SESSION['user_id'];
$patient = fetchOne("SELECT * FROM patient WHERE user_id = ?", "i", [$userId]);

// Get available doctors/staff
$doctors = fetchAll("SELECT id, full_name, specialty FROM staff WHERE role = 'doctor' ORDER BY specialty, full_name");

// Get available departments
$departments = fetchAll("SELECT DISTINCT specialty FROM staff WHERE role = 'doctor' ORDER BY specialty");

// Process form submission
$success = false;
$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $doctorId = $_POST['doctor_id'] ?? '';
    $appointmentDate = $_POST['appointment_date'] ?? '';
    $appointmentTime = $_POST['appointment_time'] ?? '';
    $reason = trim($_POST['reason'] ?? '');
    
    // Basic validation
    if (empty($doctorId) || empty($appointmentDate) || empty($appointmentTime) || empty($reason)) {
        $error = "All fields are required";
    } 
    // Validate date (must be in the future)
    else if (strtotime($appointmentDate) < strtotime(date('Y-m-d'))) {
        $error = "Appointment date must be in the future";
    } 
    // Check if the doctor exists
    else {
        $doctor = fetchOne("SELECT id FROM staff WHERE id = ?", "i", [$doctorId]);
        if (!$doctor) {
            $error = "Invalid doctor selected";
        } 
        // Check if the time slot is available
        else {
            $existingAppointment = fetchOne(
                "SELECT id FROM appointment 
                 WHERE staff_id = ? AND appointment_date = ? AND appointment_time = ? AND status != 'cancelled'", 
                "iss", [$doctorId, $appointmentDate, $appointmentTime]
            );
            
            if ($existingAppointment) {
                $error = "This time slot is already booked. Please select another time.";
            } else {
                // Insert appointment
                $sql = "INSERT INTO appointment (patient_id, staff_id, appointment_date, appointment_time, reason, status, created_at) 
                        VALUES (?, ?, ?, ?, ?, 'scheduled', NOW())";
                $stmt = executeQuery($sql, "iisss", [$patient['id'], $doctorId, $appointmentDate, $appointmentTime, $reason]);
                
                if ($stmt === false) {
                    $error = "Database error: " . $GLOBALS['conn']->error;
                } else {
                    $success = true;
                    
                    // Set success message
                    $_SESSION['flash_message'] = "Appointment booked successfully!";
                    $_SESSION['flash_type'] = "success";
                    
                    // Redirect to appointment history
                    header("Location: appointment_history.php");
                    exit;
                }
            }
        }
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-4">Book an Appointment</h1>
        <p class="text-gray-600">Schedule an appointment with one of our healthcare professionals.</p>
    </div>
    
    <?php if (!empty($error)): ?>
        <div class="bg-red-100 text-red-800 p-4 rounded-lg mb-6">
            <?= htmlspecialchars($error) ?>
        </div>
    <?php endif; ?>
    
    <div class="bg-white rounded-lg shadow-md p-6">
        <form method="POST" action="">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Department Selection -->
                <div>
                    <label for="department" class="block text-gray-700 font-medium mb-2">Department</label>
                    <select id="department" name="department" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Select Department</option>
                        <?php foreach ($departments as $department): ?>
                            <option value="<?= htmlspecialchars($department['specialty']) ?>">
                                <?= htmlspecialchars($department['specialty']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- Doctor Selection -->
                <div>
                    <label for="doctor_id" class="block text-gray-700 font-medium mb-2">Doctor</label>
                    <select id="doctor_id" name="doctor_id" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        <option value="">Select Doctor</option>
                        <?php foreach ($doctors as $doctor): ?>
                            <option value="<?= $doctor['id'] ?>" data-specialty="<?= htmlspecialchars($doctor['specialty']) ?>">
                                Dr. <?= htmlspecialchars($doctor['full_name']) ?> (<?= htmlspecialchars($doctor['specialty']) ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- Date Selection -->
                <div>
                    <label for="appointment_date" class="block text-gray-700 font-medium mb-2">Date</label>
                    <input type="date" id="appointment_date" name="appointment_date" 
                           min="<?= date('Y-m-d', strtotime('+1 day')) ?>" 
                           class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                           required>
                </div>
                
                <!-- Time Selection -->
                <div>
                    <label for="appointment_time" class="block text-gray-700 font-medium mb-2">Time</label>
                    <select id="appointment_time" name="appointment_time" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        <option value="">Select Time</option>
                        <?php
                        // Generate time slots from 8 AM to 5 PM
                        $start = strtotime('08:00');
                        $end = strtotime('17:00');
                        $interval = 30 * 60; // 30 minutes in seconds
                        
                        for ($time = $start; $time <= $end; $time += $interval) {
                            $timeStr = date('H:i:s', $time);
                            $displayTime = date('g:i A', $time);
                            echo "<option value=\"$timeStr\">$displayTime</option>";
                        }
                        ?>
                    </select>
                </div>
                
                <!-- Reason for Visit -->
                <div class="md:col-span-2">
                    <label for="reason" class="block text-gray-700 font-medium mb-2">Reason for Visit</label>
                    <textarea id="reason" name="reason" rows="4" 
                              class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                              required></textarea>
                </div>
            </div>
            
            <div class="mt-6">
                <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition">
                    Book Appointment
                </button>
            </div>
        </form>
    </div>
</div>

<!-- JavaScript for filtering doctors by department -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const departmentSelect = document.getElementById('department');
        const doctorSelect = document.getElementById('doctor_id');
        const doctorOptions = Array.from(doctorSelect.options);
        
        // Filter doctors when department changes
        departmentSelect.addEventListener('change', function() {
            const selectedDepartment = this.value;
            
            // Reset doctor select
            doctorSelect.innerHTML = '<option value="">Select Doctor</option>';
            
            // Filter doctors by department
            if (selectedDepartment) {
                doctorOptions.forEach(option => {
                    if (option.value && option.dataset.specialty === selectedDepartment) {
                        doctorSelect.appendChild(option.cloneNode(true));
                    }
                });
            } else {
                // Show all doctors if no department selected
                doctorOptions.forEach(option => {
                    if (option.value) {
                        doctorSelect.appendChild(option.cloneNode(true));
                    }
                });
            }
        });
    });
</script>

<?php include_once '../includes/footer.php'; ?>
