# 🏥 UBTH Hospital Management System

A comprehensive web-based Hospital Management System for the University of Benin Teaching Hospital (UBTH), built with PHP, MySQL, TailwindCSS, and JavaScript.

## 📌 Project Overview

This Hospital Management System (HMS) provides a secure, responsive, and user-friendly platform for managing hospital operations, including:

- **Admin Module** for hospital staff
- **User Module** for patients
- **Secure Authentication** with password hashing
- **Database with Constraints & Triggers**

## 🧱 Tech Stack

| Category       | Technology       |
|---------------|------------------|
| **Frontend**  | HTML, TailwindCSS (CDN), JavaScript |
| **Backend**   | PHP (Vanilla)    |
| **Database**  | MySQL            |
| **Auth**      | Session-based (`password_hash()`) |

## 🗂️ Project Structure

```
/UBTH_APP/
├── /admin/               # Admin modules
│   ├── dashboard.php     # Admin overview
│   ├── appointments.php  # Manage appointments
│   ├── manage_patients.php
│   ├── medication.php
│   └── emergency_room.php
├── /user/                # Patient modules
│   ├── dashboard.php     # Patient overview
│   ├── book_appointment.php
│   ├── appointment_history.php
│   └── medical_history.php
├── /auth/                # Authentication
│   ├── admin_login.php
│   ├── user_login.php
│   ├── user_register.php
│   └── logout.php
├── /includes/            # Shared components
│   ├── db.php            # DB connection
│   ├── auth.php          # Auth functions
│   ├── header.php        # Common header
│   └── footer.php        # Common footer
├── /assets/              # Static assets
│   ├── /css/             # CSS files
│   ├── /js/              # JavaScript files
│   └── /img/             # Images
├── index.php             # Landing page
└── README.md             # Project docs
```

## 🚀 Features

### Admin Module
- Dashboard with statistics
- Appointment management
- Patient management
- Medication management
- Emergency room management

### User Module
- Patient dashboard
- Book appointments
- View appointment history
- Access medical history

### Authentication
- Secure login/logout
- User registration
- Role-based access control

## 🔧 Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/ubth-hms.git
   ```

2. Set up a local web server (e.g., XAMPP, WAMP) and ensure PHP and MySQL are installed.

3. Import the database schema from the SQL file in the `database` directory.

4. Configure the database connection in `includes/db.php`.

5. Access the application through your local web server.

## 📊 Database Schema

The database includes the following tables:
- `users` - User authentication information
- `patient` - Patient details
- `staff` - Hospital staff details
- `staff_category` - Staff categories
- `staff_grade` - Staff grades
- `staff_type` - Staff types
- `appointment` - Appointment records
- `medication` - Medication records
- `emergency_room` - Emergency room records
- `bed` - Hospital bed information

## 🔒 Security Features

- Password hashing using PHP's `password_hash()`
- Session-based authentication
- Role-based access control
- Input validation and sanitization
- Prepared statements for database queries

## 📱 Responsive Design

The application is fully responsive and works on all device sizes, thanks to TailwindCSS.

## 👥 User Roles

- **Admin/Staff**: Hospital staff with access to manage patients, appointments, and hospital resources.
- **Patient**: Regular users who can book appointments and view their medical history.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Contact

For any inquiries, please contact [<EMAIL>](mailto:<EMAIL>).
