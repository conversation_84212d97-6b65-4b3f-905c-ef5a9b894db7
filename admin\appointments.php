<?php
/**
 * Admin Appointments Management
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require admin login
requireLogin('../auth/admin_login.php');
requireRole('admin', '../auth/admin_login.php');

// Process form submissions
$message = '';
$messageType = '';

// Update appointment status
if (isset($_POST['update_status'])) {
    $appointmentId = $_POST['appointment_id'];
    $newStatus = $_POST['status'];

    $result = executeQuery(
        "UPDATE appointment SET status = ? WHERE id = ?",
        "si",
        [$newStatus, $appointmentId]
    );

    if ($result) {
        $message = "Appointment status updated successfully.";
        $messageType = "success";
    } else {
        $message = "Error updating appointment status.";
        $messageType = "error";
    }
}

// Delete appointment
if (isset($_POST['delete_appointment'])) {
    $appointmentId = $_POST['appointment_id'];

    $result = executeQuery(
        "DELETE FROM appointment WHERE id = ?",
        "i",
        [$appointmentId]
    );

    if ($result) {
        $message = "Appointment deleted successfully.";
        $messageType = "success";
    } else {
        $message = "Error deleting appointment.";
        $messageType = "error";
    }
}

// Get filter parameters
$statusFilter = isset($_GET['status']) ? $_GET['status'] : '';
$dateFilter = isset($_GET['date']) ? $_GET['date'] : '';
$doctorFilter = isset($_GET['doctor']) ? $_GET['doctor'] : '';
$searchQuery = isset($_GET['search']) ? $_GET['search'] : '';

// Initialize variables with default values
$appointments = [];
$doctors = [];

// Only try to fetch data if database is available
try {
    // Check if the necessary tables exist
    $appointmentTableExists = fetchOne("SHOW TABLES LIKE 'appointment'");
    $patientTableExists = fetchOne("SHOW TABLES LIKE 'patient'");
    $staffTableExists = fetchOne("SHOW TABLES LIKE 'staff'");

    if ($appointmentTableExists && $patientTableExists && $staffTableExists) {
        // Build query based on filters
        $query = "SELECT a.*, p.full_name as patient_name, s.full_name as doctor_name, s.specialty
                  FROM appointment a
                  JOIN patient p ON a.patient_id = p.id
                  JOIN staff s ON a.staff_id = s.id
                  WHERE 1=1";
        $params = [];
        $types = "";

        if (!empty($statusFilter)) {
            $query .= " AND a.status = ?";
            $params[] = $statusFilter;
            $types .= "s";
        }

        if (!empty($dateFilter)) {
            $query .= " AND a.appointment_date = ?";
            $params[] = $dateFilter;
            $types .= "s";
        }

        if (!empty($doctorFilter)) {
            $query .= " AND a.staff_id = ?";
            $params[] = $doctorFilter;
            $types .= "i";
        }

        if (!empty($searchQuery)) {
            $query .= " AND (p.full_name LIKE ? OR s.full_name LIKE ?)";
            $params[] = "%$searchQuery%";
            $params[] = "%$searchQuery%";
            $types .= "ss";
        }

        $query .= " ORDER BY a.appointment_date DESC, a.appointment_time DESC";

        // Get appointments based on filters
        $appointments = fetchAll($query, $types, $params) ?: [];

        // Get all doctors for filter dropdown
        if ($staffTableExists) {
            $doctors = fetchAll("SELECT id, full_name FROM staff WHERE role = 'doctor' ORDER BY full_name") ?: [];
        }
    }
} catch (Exception $e) {
    // If there's an error, we'll just use the default empty arrays
    // This allows the page to load even if the database is not set up
}

// Include header
include_once '../includes/header.php';

// Set flash message if exists
if (!empty($message)) {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $messageType;
}
?>

<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-4 md:mb-0">Manage Appointments</h1>
        <a href="dashboard.php" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition">
            <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
        </a>
    </div>

    <!-- Filters -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 class="text-lg font-semibold text-gray-700 mb-3">Filter Appointments</h2>
        <form action="" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select id="status" name="status" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="">All Statuses</option>
                    <option value="scheduled" <?= $statusFilter === 'scheduled' ? 'selected' : '' ?>>Scheduled</option>
                    <option value="completed" <?= $statusFilter === 'completed' ? 'selected' : '' ?>>Completed</option>
                    <option value="cancelled" <?= $statusFilter === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                </select>
            </div>

            <div>
                <label for="date" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <input type="date" id="date" name="date" value="<?= htmlspecialchars($dateFilter) ?>" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
            </div>

            <div>
                <label for="doctor" class="block text-sm font-medium text-gray-700 mb-1">Doctor</label>
                <select id="doctor" name="doctor" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="">All Doctors</option>
                    <?php foreach ($doctors as $doctor): ?>
                        <option value="<?= $doctor['id'] ?>" <?= $doctorFilter == $doctor['id'] ? 'selected' : '' ?>>
                            Dr. <?= htmlspecialchars($doctor['full_name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <div class="relative">
                    <input type="text" id="search" name="search" value="<?= htmlspecialchars($searchQuery) ?>" placeholder="Search by name..." class="w-full rounded-lg border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 pl-10">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>

            <div class="md:col-span-4 flex justify-end space-x-2">
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition">
                    <i class="fas fa-filter mr-2"></i> Apply Filters
                </button>
                <a href="appointments.php" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition">
                    <i class="fas fa-times mr-2"></i> Clear Filters
                </a>
            </div>
        </form>
    </div>

    <!-- Appointments Table -->
    <div class="bg-white rounded-lg overflow-hidden">
        <?php if (empty($appointments)): ?>
            <div class="text-center py-8">
                <img src="https://images.unsplash.com/photo-1579684385127-1ef15d508118?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=880&q=80"
                     alt="No appointments" class="w-32 h-32 object-cover rounded-full mx-auto mb-4">
                <p class="text-gray-500 italic mb-2">No appointments found.</p>
                <p class="text-sm text-gray-600">Try adjusting your filters or search criteria.</p>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Doctor</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($appointments as $appointment): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($appointment['patient_name']) ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">Dr. <?= htmlspecialchars($appointment['doctor_name']) ?></div>
                                    <div class="text-sm text-gray-500"><?= htmlspecialchars($appointment['specialty']) ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?= date('F j, Y', strtotime($appointment['appointment_date'])) ?></div>
                                    <div class="text-sm text-gray-500"><?= date('g:i A', strtotime($appointment['appointment_time'])) ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                    $statusClass = '';
                                    switch ($appointment['status']) {
                                        case 'scheduled':
                                            $statusClass = 'bg-blue-100 text-blue-800';
                                            break;
                                        case 'completed':
                                            $statusClass = 'bg-green-100 text-green-800';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'bg-red-100 text-red-800';
                                            break;
                                        default:
                                            $statusClass = 'bg-gray-100 text-gray-800';
                                    }
                                    ?>
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?= $statusClass ?>">
                                        <?= ucfirst(htmlspecialchars($appointment['status'])) ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button type="button" class="text-blue-600 hover:text-blue-900"
                                                onclick="openUpdateModal(<?= $appointment['id'] ?>, '<?= $appointment['status'] ?>')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="text-red-600 hover:text-red-900"
                                                onclick="openDeleteModal(<?= $appointment['id'] ?>)">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Update Status Modal -->
<div id="updateStatusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Update Appointment Status</h3>
            <button type="button" class="text-gray-400 hover:text-gray-500" onclick="closeUpdateModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form action="" method="POST">
            <input type="hidden" id="update_appointment_id" name="appointment_id">
            <div class="mb-4">
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select id="update_status" name="status" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="scheduled">Scheduled</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>
            <div class="flex justify-end space-x-2">
                <button type="button" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition" onclick="closeUpdateModal()">
                    Cancel
                </button>
                <button type="submit" name="update_status" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition">
                    Update Status
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Delete Appointment</h3>
            <button type="button" class="text-gray-400 hover:text-gray-500" onclick="closeDeleteModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <p class="mb-4 text-gray-600">Are you sure you want to delete this appointment? This action cannot be undone.</p>
        <form action="" method="POST">
            <input type="hidden" id="delete_appointment_id" name="appointment_id">
            <div class="flex justify-end space-x-2">
                <button type="button" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition" onclick="closeDeleteModal()">
                    Cancel
                </button>
                <button type="submit" name="delete_appointment" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition">
                    Delete
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    // Update Status Modal Functions
    function openUpdateModal(appointmentId, currentStatus) {
        document.getElementById('update_appointment_id').value = appointmentId;
        document.getElementById('update_status').value = currentStatus;
        document.getElementById('updateStatusModal').classList.remove('hidden');
        document.getElementById('updateStatusModal').classList.add('flex');
    }

    function closeUpdateModal() {
        document.getElementById('updateStatusModal').classList.add('hidden');
        document.getElementById('updateStatusModal').classList.remove('flex');
    }

    // Delete Modal Functions
    function openDeleteModal(appointmentId) {
        document.getElementById('delete_appointment_id').value = appointmentId;
        document.getElementById('deleteModal').classList.remove('hidden');
        document.getElementById('deleteModal').classList.add('flex');
    }

    function closeDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
        document.getElementById('deleteModal').classList.remove('flex');
    }
</script>

<?php include_once '../includes/footer.php'; ?>
