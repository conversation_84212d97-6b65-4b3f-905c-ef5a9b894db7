<?php
/**
 * Medication Entry Form
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require admin login
requireLogin('../auth/admin_login.php');
requireRole('admin', '../auth/admin_login.php');

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $medication_name = $_POST['medication_name'] ?? '';
    $generic_name = $_POST['generic_name'] ?? '';
    $manufacturer = $_POST['manufacturer'] ?? '';
    $dosage_form = $_POST['dosage_form'] ?? '';
    $strength = $_POST['strength'] ?? '';
    $unit = $_POST['unit'] ?? '';
    $quantity_in_stock = $_POST['quantity_in_stock'] ?? '';
    $unit_price = $_POST['unit_price'] ?? '';
    $expiry_date = $_POST['expiry_date'] ?? '';
    $batch_number = $_POST['batch_number'] ?? '';
    $supplier = $_POST['supplier'] ?? '';
    $description = $_POST['description'] ?? '';
    $storage_conditions = $_POST['storage_conditions'] ?? '';
    
    // Validation
    $errors = [];
    if (empty($medication_name)) $errors[] = "Medication name is required";
    if (empty($dosage_form)) $errors[] = "Dosage form is required";
    if (empty($strength)) $errors[] = "Strength is required";
    if (empty($unit)) $errors[] = "Unit is required";
    if (empty($quantity_in_stock)) $errors[] = "Quantity in stock is required";
    if (empty($unit_price)) $errors[] = "Unit price is required";
    if (empty($expiry_date)) $errors[] = "Expiry date is required";
    
    // Check if expiry date is not in the past
    if (!empty($expiry_date) && $expiry_date < date('Y-m-d')) {
        $errors[] = "Expiry date cannot be in the past";
    }
    
    if (empty($errors)) {
        try {
            // Insert medication record
            $sql = "INSERT INTO medication (medication_name, generic_name, manufacturer, dosage_form, strength, unit, quantity_in_stock, unit_price, expiry_date, batch_number, supplier, description, storage_conditions, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            $result = executeQuery($sql, "ssssssissssss", [
                $medication_name, $generic_name, $manufacturer, $dosage_form, $strength, $unit,
                $quantity_in_stock, $unit_price, $expiry_date, $batch_number, $supplier, $description, $storage_conditions
            ]);
            
            if ($result) {
                $_SESSION['flash_message'] = "Medication added successfully!";
                $_SESSION['flash_type'] = "success";
                header("Location: medication_entry.php");
                exit;
            } else {
                $errors[] = "Failed to add medication. Please try again.";
            }
        } catch (Exception $e) {
            $errors[] = "Database error: " . $e->getMessage();
        }
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl shadow-lg p-8 mb-8">
        <div class="flex items-center">
            <i class="fas fa-pills text-4xl mr-4"></i>
            <div>
                <h1 class="text-3xl font-bold">Medication Entry</h1>
                <p class="text-indigo-100 mt-1">Add new medications to the hospital inventory</p>
            </div>
        </div>
    </div>

    <!-- Display Errors -->
    <?php if (!empty($errors)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <ul class="list-disc list-inside">
                <?php foreach ($errors as $error): ?>
                    <li><?= htmlspecialchars($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Medication Form -->
    <div class="bg-white rounded-xl shadow-lg p-8">
        <form method="POST" class="space-y-6">
            <!-- Basic Information Section -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                    Basic Information
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Medication Name *</label>
                        <input type="text" name="medication_name" required 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Enter medication name"
                               value="<?= htmlspecialchars($_POST['medication_name'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Generic Name</label>
                        <input type="text" name="generic_name" 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Enter generic name"
                               value="<?= htmlspecialchars($_POST['generic_name'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Manufacturer</label>
                        <input type="text" name="manufacturer" 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Enter manufacturer name"
                               value="<?= htmlspecialchars($_POST['manufacturer'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Supplier</label>
                        <input type="text" name="supplier" 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Enter supplier name"
                               value="<?= htmlspecialchars($_POST['supplier'] ?? '') ?>">
                    </div>
                </div>
            </div>

            <!-- Dosage Information Section -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-prescription-bottle text-green-600 mr-2"></i>
                    Dosage Information
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Dosage Form *</label>
                        <select name="dosage_form" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Dosage Form</option>
                            <option value="tablet" <?= ($_POST['dosage_form'] ?? '') === 'tablet' ? 'selected' : '' ?>>Tablet</option>
                            <option value="capsule" <?= ($_POST['dosage_form'] ?? '') === 'capsule' ? 'selected' : '' ?>>Capsule</option>
                            <option value="syrup" <?= ($_POST['dosage_form'] ?? '') === 'syrup' ? 'selected' : '' ?>>Syrup</option>
                            <option value="injection" <?= ($_POST['dosage_form'] ?? '') === 'injection' ? 'selected' : '' ?>>Injection</option>
                            <option value="cream" <?= ($_POST['dosage_form'] ?? '') === 'cream' ? 'selected' : '' ?>>Cream</option>
                            <option value="ointment" <?= ($_POST['dosage_form'] ?? '') === 'ointment' ? 'selected' : '' ?>>Ointment</option>
                            <option value="drops" <?= ($_POST['dosage_form'] ?? '') === 'drops' ? 'selected' : '' ?>>Drops</option>
                            <option value="inhaler" <?= ($_POST['dosage_form'] ?? '') === 'inhaler' ? 'selected' : '' ?>>Inhaler</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Strength *</label>
                        <input type="text" name="strength" required 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="e.g., 500"
                               value="<?= htmlspecialchars($_POST['strength'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Unit *</label>
                        <select name="unit" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Unit</option>
                            <option value="mg" <?= ($_POST['unit'] ?? '') === 'mg' ? 'selected' : '' ?>>mg</option>
                            <option value="g" <?= ($_POST['unit'] ?? '') === 'g' ? 'selected' : '' ?>>g</option>
                            <option value="ml" <?= ($_POST['unit'] ?? '') === 'ml' ? 'selected' : '' ?>>ml</option>
                            <option value="mcg" <?= ($_POST['unit'] ?? '') === 'mcg' ? 'selected' : '' ?>>mcg</option>
                            <option value="IU" <?= ($_POST['unit'] ?? '') === 'IU' ? 'selected' : '' ?>>IU</option>
                            <option value="%" <?= ($_POST['unit'] ?? '') === '%' ? 'selected' : '' ?>>%</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Inventory Information Section -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-warehouse text-purple-600 mr-2"></i>
                    Inventory Information
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Quantity in Stock *</label>
                        <input type="number" name="quantity_in_stock" required min="0"
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Enter quantity"
                               value="<?= htmlspecialchars($_POST['quantity_in_stock'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Unit Price *</label>
                        <input type="number" name="unit_price" required step="0.01" min="0"
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Enter unit price"
                               value="<?= htmlspecialchars($_POST['unit_price'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Expiry Date *</label>
                        <input type="date" name="expiry_date" required 
                               class="form-input border-gray-300 focus:border-blue-500"
                               min="<?= date('Y-m-d') ?>"
                               value="<?= htmlspecialchars($_POST['expiry_date'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Batch Number</label>
                        <input type="text" name="batch_number" 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Enter batch number"
                               value="<?= htmlspecialchars($_POST['batch_number'] ?? '') ?>">
                    </div>
                </div>
            </div>

            <!-- Additional Information Section -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-clipboard text-orange-600 mr-2"></i>
                    Additional Information
                </h3>
                <div class="space-y-6">
                    <div>
                        <label class="form-label text-gray-700">Description</label>
                        <textarea name="description" rows="3" 
                                  class="form-input border-gray-300 focus:border-blue-500" 
                                  placeholder="Enter medication description, uses, etc."><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Storage Conditions</label>
                        <textarea name="storage_conditions" rows="2" 
                                  class="form-input border-gray-300 focus:border-blue-500" 
                                  placeholder="Enter storage requirements (temperature, humidity, etc.)"><?= htmlspecialchars($_POST['storage_conditions'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="window.history.back()" 
                        class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
                    Cancel
                </button>
                <button type="submit" 
                        class="btn-primary px-8 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition">
                    <i class="fas fa-save mr-2"></i>
                    Add Medication
                </button>
            </div>
        </form>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
