<?php
/**
 * Appointment History Page
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require patient login
requireLogin('../auth/user_login.php');
requireRole('patient', '../auth/user_login.php');

// Get patient information
$userId = $_SESSION['user_id'];
$patient = fetchOne("SELECT * FROM patient WHERE user_id = ?", "i", [$userId]);

// Handle appointment cancellation
if (isset($_GET['action']) && $_GET['action'] === 'cancel' && isset($_GET['id'])) {
    $appointmentId = $_GET['id'];
    
    // Check if appointment belongs to the patient
    $appointment = fetchOne(
        "SELECT a.* FROM appointment a 
         JOIN patient p ON a.patient_id = p.id 
         WHERE a.id = ? AND p.user_id = ? AND a.status = 'scheduled'", 
        "ii", [$appointmentId, $userId]
    );
    
    if ($appointment) {
        // Cancel appointment
        $result = executeQuery(
            "UPDATE appointment SET status = 'cancelled', updated_at = NOW() WHERE id = ?", 
            "i", [$appointmentId]
        );
        
        if ($result) {
            $_SESSION['flash_message'] = "Appointment cancelled successfully.";
            $_SESSION['flash_type'] = "success";
        } else {
            $_SESSION['flash_message'] = "Failed to cancel appointment.";
            $_SESSION['flash_type'] = "error";
        }
    } else {
        $_SESSION['flash_message'] = "Invalid appointment or you don't have permission to cancel it.";
        $_SESSION['flash_type'] = "error";
    }
    
    // Redirect to remove the action from URL
    header("Location: appointment_history.php");
    exit;
}

// Get appointment history
$appointments = fetchAll(
    "SELECT a.*, s.full_name as doctor_name, s.specialty 
     FROM appointment a 
     JOIN staff s ON a.staff_id = s.id 
     JOIN patient p ON a.patient_id = p.id 
     WHERE p.user_id = ? 
     ORDER BY a.appointment_date DESC, a.appointment_time DESC", 
    "i", [$userId]
);

// Include header
include_once '../includes/header.php';
?>

<div class="max-w-6xl mx-auto">
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-800 mb-2">Appointment History</h1>
                <p class="text-gray-600">View and manage your appointments.</p>
            </div>
            <a href="book_appointment.php" class="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition">
                Book New Appointment
            </a>
        </div>
    </div>
    
    <?php if (empty($appointments)): ?>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="text-center py-8">
                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <h2 class="text-xl font-semibold text-gray-700 mb-2">No Appointments Found</h2>
                <p class="text-gray-500 mb-6">You haven't scheduled any appointments yet.</p>
                <a href="book_appointment.php" class="bg-blue-600 text-white py-2 px-6 rounded-lg hover:bg-blue-700 transition">
                    Book Your First Appointment
                </a>
            </div>
        </div>
    <?php else: ?>
        <!-- Upcoming Appointments -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Upcoming Appointments</h2>
            
            <?php
            $hasUpcoming = false;
            foreach ($appointments as $appointment) {
                if ($appointment['status'] === 'scheduled' && strtotime($appointment['appointment_date']) >= strtotime(date('Y-m-d'))) {
                    $hasUpcoming = true;
                    break;
                }
            }
            ?>
            
            <?php if (!$hasUpcoming): ?>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-gray-500 italic">No upcoming appointments.</p>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-4 py-2 text-left text-gray-700">Date</th>
                                <th class="px-4 py-2 text-left text-gray-700">Time</th>
                                <th class="px-4 py-2 text-left text-gray-700">Doctor</th>
                                <th class="px-4 py-2 text-left text-gray-700">Department</th>
                                <th class="px-4 py-2 text-left text-gray-700">Reason</th>
                                <th class="px-4 py-2 text-left text-gray-700">Status</th>
                                <th class="px-4 py-2 text-left text-gray-700">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($appointments as $appointment): ?>
                                <?php if ($appointment['status'] === 'scheduled' && strtotime($appointment['appointment_date']) >= strtotime(date('Y-m-d'))): ?>
                                    <tr class="border-t">
                                        <td class="px-4 py-3"><?= date('M j, Y', strtotime($appointment['appointment_date'])) ?></td>
                                        <td class="px-4 py-3"><?= date('g:i A', strtotime($appointment['appointment_time'])) ?></td>
                                        <td class="px-4 py-3">Dr. <?= htmlspecialchars($appointment['doctor_name']) ?></td>
                                        <td class="px-4 py-3"><?= htmlspecialchars($appointment['specialty']) ?></td>
                                        <td class="px-4 py-3"><?= htmlspecialchars($appointment['reason']) ?></td>
                                        <td class="px-4 py-3">
                                            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Scheduled</span>
                                        </td>
                                        <td class="px-4 py-3">
                                            <a href="?action=cancel&id=<?= $appointment['id'] ?>" 
                                               class="text-red-600 hover:underline"
                                               onclick="return confirm('Are you sure you want to cancel this appointment?')">
                                                Cancel
                                            </a>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Past Appointments -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Past Appointments</h2>
            
            <?php
            $hasPast = false;
            foreach ($appointments as $appointment) {
                if ($appointment['status'] === 'completed' || 
                    ($appointment['status'] === 'scheduled' && strtotime($appointment['appointment_date']) < strtotime(date('Y-m-d'))) ||
                    $appointment['status'] === 'cancelled') {
                    $hasPast = true;
                    break;
                }
            }
            ?>
            
            <?php if (!$hasPast): ?>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-gray-500 italic">No past appointments.</p>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-4 py-2 text-left text-gray-700">Date</th>
                                <th class="px-4 py-2 text-left text-gray-700">Time</th>
                                <th class="px-4 py-2 text-left text-gray-700">Doctor</th>
                                <th class="px-4 py-2 text-left text-gray-700">Department</th>
                                <th class="px-4 py-2 text-left text-gray-700">Reason</th>
                                <th class="px-4 py-2 text-left text-gray-700">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($appointments as $appointment): ?>
                                <?php 
                                $isPast = $appointment['status'] === 'completed' || 
                                          ($appointment['status'] === 'scheduled' && strtotime($appointment['appointment_date']) < strtotime(date('Y-m-d'))) ||
                                          $appointment['status'] === 'cancelled';
                                
                                if ($isPast):
                                ?>
                                    <tr class="border-t">
                                        <td class="px-4 py-3"><?= date('M j, Y', strtotime($appointment['appointment_date'])) ?></td>
                                        <td class="px-4 py-3"><?= date('g:i A', strtotime($appointment['appointment_time'])) ?></td>
                                        <td class="px-4 py-3">Dr. <?= htmlspecialchars($appointment['doctor_name']) ?></td>
                                        <td class="px-4 py-3"><?= htmlspecialchars($appointment['specialty']) ?></td>
                                        <td class="px-4 py-3"><?= htmlspecialchars($appointment['reason']) ?></td>
                                        <td class="px-4 py-3">
                                            <?php if ($appointment['status'] === 'completed'): ?>
                                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Completed</span>
                                            <?php elseif ($appointment['status'] === 'cancelled'): ?>
                                                <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Cancelled</span>
                                            <?php elseif (strtotime($appointment['appointment_date']) < strtotime(date('Y-m-d'))): ?>
                                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Missed</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<?php include_once '../includes/footer.php'; ?>
