<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include auth functions if not already included
if (!function_exists('isLoggedIn')) {
    require_once __DIR__ . '/auth.php';
}

// Determine current page for navigation highlighting
$current_page = basename($_SERVER['PHP_SELF']);
?>
<!DOCTYPE html>
<html lang="en" style="background-color: #e6f3ff !important;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UBTH Hospital Management System</title>
    <!-- Tailwind CSS from CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation Library -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css">
    <!-- Custom styles -->
    <link rel="stylesheet" href="assets/css/main.css">
    <style>
        body {
            background-color: #f0f8ff !important;
            font-family: 'Poppins', sans-serif;
        }
        header {
            background: linear-gradient(135deg, #00a86b, #0077cc) !important;
            color: white;
        }
        footer {
            background: linear-gradient(135deg, #005ea3, #008c58) !important;
            color: white;
        }
        .bg-primary {
            background-color: #00a86b !important;
        }
        .bg-secondary {
            background-color: #0077cc !important;
        }
        .text-primary {
            color: #00a86b !important;
        }
        .text-secondary {
            color: #0077cc !important;
        }
        .active-nav {
            background-color: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#00a86b',
                        secondary: '#0077cc',
                        accent: '#ff6b6b',
                        'light-bg': '#f0f8ff'
                    }
                }
            }
        }
    </script>
</head>
<body style="background-color: #e6f3ff !important; min-height: 100vh; display: flex; flex-direction: column;" bgcolor="#e6f3ff">
    <!-- Header/Navbar -->
    <header style="background: linear-gradient(135deg, #00a86b, #0077cc) !important; color: white; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <div class="container mx-auto px-4 py-2">
            <div class="flex justify-between items-center">
                <!-- Logo -->
                <div class="logo-container">
                    <img src="assets/img/logo-green-removebg-preview.png" alt="UBTH Logo" class="logo-img h-12 md:h-14">
                    <div class="flex flex-col ml-2">
                        <span class="text-xl md:text-2xl font-bold logo-text">UBTH</span>
                        <span class="hidden md:inline-block text-xs">University of Benin Teaching Hospital</span>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="hidden md:flex space-x-4">
                    <a href="index.php" class="px-3 py-2 rounded transition flex items-center <?= $current_page === 'index.php' ? 'active-nav' : '' ?>">
                        <i class="fas fa-home mr-1"></i> Home
                    </a>

                    <?php if (isLoggedIn()): ?>
                        <?php if (hasRole('admin')): ?>
                            <a href="admin/dashboard.php" class="px-3 py-2 rounded transition flex items-center <?= strpos($current_page, 'dashboard.php') !== false && strpos($_SERVER['PHP_SELF'], '/admin/') !== false ? 'active-nav' : '' ?>">
                                <i class="fas fa-chart-line mr-1"></i> Dashboard
                            </a>
                        <?php elseif (hasRole('patient')): ?>
                            <a href="user/dashboard.php" class="px-3 py-2 rounded transition flex items-center <?= strpos($current_page, 'dashboard.php') !== false && strpos($_SERVER['PHP_SELF'], '/user/') !== false ? 'active-nav' : '' ?>">
                                <i class="fas fa-columns mr-1"></i> Dashboard
                            </a>
                            <a href="user/book_appointment.php" class="px-3 py-2 rounded transition flex items-center <?= $current_page === 'book_appointment.php' ? 'active-nav' : '' ?>">
                                <i class="fas fa-calendar-plus mr-1"></i> Book Appointment
                            </a>
                            <a href="user/appointment_history.php" class="px-3 py-2 rounded transition flex items-center <?= $current_page === 'appointment_history.php' ? 'active-nav' : '' ?>">
                                <i class="fas fa-calendar-check mr-1"></i> Appointments
                            </a>
                            <a href="user/medical_history.php" class="px-3 py-2 rounded transition flex items-center <?= $current_page === 'medical_history.php' ? 'active-nav' : '' ?>">
                                <i class="fas fa-notes-medical mr-1"></i> Medical History
                            </a>
                        <?php endif; ?>
                        <a href="auth/logout.php" class="px-3 py-2 rounded transition flex items-center">
                            <i class="fas fa-sign-out-alt mr-1"></i> Logout
                        </a>
                    <?php else: ?>
                        <a href="auth/user_login.php" class="px-3 py-2 rounded transition flex items-center <?= $current_page === 'user_login.php' ? 'active-nav' : '' ?>">
                            <i class="fas fa-user mr-1"></i> Patient Login
                        </a>
                        <a href="auth/admin_login.php" class="px-3 py-2 rounded transition flex items-center <?= $current_page === 'admin_login.php' ? 'active-nav' : '' ?>">
                            <i class="fas fa-user-md mr-1"></i> Staff Login
                        </a>
                        <a href="auth/user_register.php" class="px-3 py-2 rounded transition flex items-center <?= $current_page === 'user_register.php' ? 'active-nav' : '' ?>">
                            <i class="fas fa-user-plus mr-1"></i> Register
                        </a>
                    <?php endif; ?>
                </nav>

                <!-- Mobile menu button -->
                <button id="mobile-menu-button" class="md:hidden text-white focus:outline-none">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile menu -->
            <div id="mobile-menu" class="md:hidden hidden mt-2 pb-2">
                <a href="index.php" class="block px-3 py-2 rounded transition flex items-center <?= $current_page === 'index.php' ? 'active-nav' : '' ?>">
                    <i class="fas fa-home mr-2"></i> Home
                </a>

                <?php if (isLoggedIn()): ?>
                    <?php if (hasRole('admin')): ?>
                        <a href="admin/dashboard.php" class="block px-3 py-2 rounded transition flex items-center <?= strpos($current_page, 'dashboard.php') !== false && strpos($_SERVER['PHP_SELF'], '/admin/') !== false ? 'active-nav' : '' ?>">
                            <i class="fas fa-chart-line mr-2"></i> Dashboard
                        </a>
                    <?php elseif (hasRole('patient')): ?>
                        <a href="user/dashboard.php" class="block px-3 py-2 rounded transition flex items-center <?= strpos($current_page, 'dashboard.php') !== false && strpos($_SERVER['PHP_SELF'], '/user/') !== false ? 'active-nav' : '' ?>">
                            <i class="fas fa-columns mr-2"></i> Dashboard
                        </a>
                        <a href="user/book_appointment.php" class="block px-3 py-2 rounded transition flex items-center <?= $current_page === 'book_appointment.php' ? 'active-nav' : '' ?>">
                            <i class="fas fa-calendar-plus mr-2"></i> Book Appointment
                        </a>
                        <a href="user/appointment_history.php" class="block px-3 py-2 rounded transition flex items-center <?= $current_page === 'appointment_history.php' ? 'active-nav' : '' ?>">
                            <i class="fas fa-calendar-check mr-2"></i> Appointments
                        </a>
                        <a href="user/medical_history.php" class="block px-3 py-2 rounded transition flex items-center <?= $current_page === 'medical_history.php' ? 'active-nav' : '' ?>">
                            <i class="fas fa-notes-medical mr-2"></i> Medical History
                        </a>
                    <?php endif; ?>
                    <a href="auth/logout.php" class="block px-3 py-2 rounded transition flex items-center">
                        <i class="fas fa-sign-out-alt mr-2"></i> Logout
                    </a>
                <?php else: ?>
                    <a href="auth/user_login.php" class="block px-3 py-2 rounded transition flex items-center <?= $current_page === 'user_login.php' ? 'active-nav' : '' ?>">
                        <i class="fas fa-user mr-2"></i> Patient Login
                    </a>
                    <a href="auth/admin_login.php" class="block px-3 py-2 rounded transition flex items-center <?= $current_page === 'admin_login.php' ? 'active-nav' : '' ?>">
                        <i class="fas fa-user-md mr-2"></i> Staff Login
                    </a>
                    <a href="auth/user_register.php" class="block px-3 py-2 rounded transition flex items-center <?= $current_page === 'user_register.php' ? 'active-nav' : '' ?>">
                        <i class="fas fa-user-plus mr-2"></i> Register
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <!-- Main Content Container -->
    <main class="container mx-auto px-4 py-6" style="background-color: #e6f3ff !important;">
        <?php if (isset($_SESSION['flash_message'])): ?>
            <div class="mb-4 p-4 rounded <?= $_SESSION['flash_type'] === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                <?= $_SESSION['flash_message'] ?>
            </div>
            <?php unset($_SESSION['flash_message'], $_SESSION['flash_type']); ?>
        <?php endif; ?>
