# 🏥 UBTH Hospital Management System (HMS)  
**Full Stack Web Application**  
*Built with PHP, MySQL, TailwindCSS, and JavaScript*  

---

## 📌 **Project Overview**  
You are tasked with developing a **secure, responsive, and user-friendly** web-based **Hospital Management System (HMS)** for the **University of Benin Teaching Hospital (UBTH)**.  

The system must support:  
✅ **Admin Module** (Hospital Staff)  
✅ **User Module** (Patients)  
✅ **Secure Authentication** (Login/Logout)  
✅ **Database with Constraints & Triggers**  

---

## 🎯 **Objectives**  
  
1. **Build frontend** with **HTML, TailwindCSS (CDN), JavaScript**.  
2. **Backend logic** in **PHP** (Vanilla, no frameworks).  
3. **Session-based authentication** (`password_hash()` for security).  
4. **Prevent duplicate entries** & enforce **data validation**.  
5. **Role-based access control** (Admin vs. Patient).  

---

## 🧱 **Tech Stack**  
| Category       | Technology       |
|---------------|------------------|
| **Frontend**  | HTML, TailwindCSS, JavaScript |
| **Backend**   | PHP (Vanilla)    |
| **Database**  | MySQL            |
| **Auth**      | Session-based (`password_hash()`) |

---

## 🗂️ **Project Structure**  
```bash
/ubth-hms/
├── /admin/               # Admin modules
│   ├── dashboard.php     # Admin overview
│   ├── manage_patients.php
│   ├── manage_doctors.php
│   ├── appointments.php
│   ├── lab_reports.php
│   ├── billing.php
│   └── inventory.php
├── /user/                # Patient modules
│   ├── dashboard.php     # Patient overview
│   ├── book_appointment.php
│   ├── appointment_history.php
│   └── medical_history.php
├── /auth/                # Authentication
│   ├── admin_login.php
│   ├── user_login.php
│   ├── user_register.php
│   └── logout.php
├── /includes/            # Shared components
│   ├── db.php            # DB connection
│   ├── auth.php          # Auth functions
│   ├── header.php        # Common header
│   └── footer.php        # Common footer
├── index.php             # Landing page
└── README.md             # Project docs