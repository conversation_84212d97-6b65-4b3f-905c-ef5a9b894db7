<?php
/**
 * User/Patient Dashboard
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require patient login
requireLogin('../auth/user_login.php');
requireRole('patient', '../auth/user_login.php');

// Get patient information
$userId = $_SESSION['user_id'];
$patient = fetchOne("SELECT * FROM patient WHERE user_id = ?", "i", [$userId]);

// Get upcoming appointments
$upcomingAppointments = fetchAll(
    "SELECT a.*, s.full_name as doctor_name, s.specialty
     FROM appointment a
     JOIN staff s ON a.staff_id = s.id
     WHERE a.patient_id = ? AND a.appointment_date >= CURDATE()
     ORDER BY a.appointment_date, a.appointment_time
     LIMIT 5",
    "i", [$patient['id']]
);

// Get recent medical history
$recentMedicalHistory = fetchAll(
    "SELECT * FROM medication
     WHERE patient_id = ?
     ORDER BY prescribed_date DESC
     LIMIT 5",
    "i", [$patient['id']]
);

// Include header
include_once '../includes/header.php';
?>

<div class="bg-gradient-to-r from-primary to-secondary text-white rounded-lg shadow-md p-8 mb-6 slide-in-up">
    <div class="flex flex-col md:flex-row items-center justify-between">
        <div>
            <h1 class="text-2xl md:text-3xl font-bold mb-2">Welcome, <?= htmlspecialchars($_SESSION['full_name']) ?></h1>
            <p class="text-white text-opacity-90">Manage your appointments and view your medical history.</p>
        </div>
        <div class="mt-4 md:mt-0">
            <img src="https://images.unsplash.com/photo-1505751172876-fa1923c5c528?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80"
                 alt="Healthcare" class="w-32 h-32 object-cover rounded-full border-4 border-white">
        </div>
    </div>
</div>

<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow-md p-6 slide-in-up" style="animation-delay: 0.1s;">
        <div class="flex items-center mb-4">
            <div class="bg-primary bg-opacity-10 p-2 rounded-full mr-3">
                <i class="fas fa-bolt text-primary text-xl"></i>
            </div>
            <h2 class="text-xl font-semibold text-gray-800">Quick Actions</h2>
        </div>
        <div class="space-y-3">
            <a href="book_appointment.php" class="flex items-center p-4 bg-primary bg-opacity-10 text-primary rounded-lg hover:bg-opacity-20 transition">
                <i class="fas fa-calendar-plus text-xl mr-3"></i>
                <div>
                    <div class="font-medium">Book New Appointment</div>
                    <div class="text-sm text-gray-600">Schedule a visit with a doctor</div>
                </div>
            </a>
            <a href="appointment_history.php" class="flex items-center p-4 bg-secondary bg-opacity-10 text-secondary rounded-lg hover:bg-opacity-20 transition">
                <i class="fas fa-calendar-check text-xl mr-3"></i>
                <div>
                    <div class="font-medium">View Appointment History</div>
                    <div class="text-sm text-gray-600">See your past and upcoming appointments</div>
                </div>
            </a>
            <a href="medical_history.php" class="flex items-center p-4 bg-primary bg-opacity-10 text-primary rounded-lg hover:bg-opacity-20 transition">
                <i class="fas fa-notes-medical text-xl mr-3"></i>
                <div>
                    <div class="font-medium">View Medical History</div>
                    <div class="text-sm text-gray-600">Access your medical records</div>
                </div>
            </a>
            <a href="#" class="flex items-center p-4 bg-secondary bg-opacity-10 text-secondary rounded-lg hover:bg-opacity-20 transition">
                <i class="fas fa-user-edit text-xl mr-3"></i>
                <div>
                    <div class="font-medium">Update Personal Information</div>
                    <div class="text-sm text-gray-600">Keep your profile up to date</div>
                </div>
            </a>
        </div>
    </div>

    <!-- Upcoming Appointments -->
    <div class="bg-white rounded-lg shadow-md p-6 slide-in-up" style="animation-delay: 0.2s;">
        <div class="flex items-center mb-4">
            <div class="bg-secondary bg-opacity-10 p-2 rounded-full mr-3">
                <i class="fas fa-calendar-alt text-secondary text-xl"></i>
            </div>
            <h2 class="text-xl font-semibold text-gray-800">Upcoming Appointments</h2>
        </div>

        <?php if (empty($upcomingAppointments)): ?>
            <div class="bg-gray-50 p-6 rounded-lg text-center">
                <img src="https://images.unsplash.com/photo-1579684385127-1ef15d508118?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=880&q=80"
                     alt="No appointments" class="w-32 h-32 object-cover rounded-full mx-auto mb-4">
                <p class="text-gray-500 italic mb-4">No upcoming appointments.</p>
                <a href="book_appointment.php" class="inline-flex items-center px-4 py-2 bg-secondary text-white rounded-lg hover:bg-secondary-dark transition">
                    <i class="fas fa-plus-circle mr-2"></i> Book an appointment
                </a>
            </div>
        <?php else: ?>
            <div class="space-y-4">
                <?php foreach ($upcomingAppointments as $appointment): ?>
                    <div class="bg-secondary bg-opacity-5 rounded-lg p-4 border-l-4 border-secondary">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-calendar-day text-secondary mr-2"></i>
                            <div class="font-semibold"><?= date('l, F j, Y', strtotime($appointment['appointment_date'])) ?></div>
                        </div>
                        <div class="flex items-center mb-2">
                            <i class="fas fa-clock text-secondary mr-2"></i>
                            <div class="text-gray-600"><?= date('g:i A', strtotime($appointment['appointment_time'])) ?></div>
                        </div>
                        <div class="flex items-center mb-2">
                            <i class="fas fa-user-md text-secondary mr-2"></i>
                            <div>Dr. <?= htmlspecialchars($appointment['doctor_name']) ?></div>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-stethoscope text-secondary mr-2"></i>
                            <div class="text-sm text-gray-600"><?= htmlspecialchars($appointment['specialty']) ?></div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="mt-6 text-center">
                <a href="appointment_history.php" class="inline-flex items-center text-secondary hover:text-secondary-dark transition">
                    View all appointments <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Recent Medical History -->
    <div class="bg-white rounded-lg shadow-md p-6 slide-in-up" style="animation-delay: 0.3s;">
        <div class="flex items-center mb-4">
            <div class="bg-primary bg-opacity-10 p-2 rounded-full mr-3">
                <i class="fas fa-pills text-primary text-xl"></i>
            </div>
            <h2 class="text-xl font-semibold text-gray-800">Recent Medications</h2>
        </div>

        <?php if (empty($recentMedicalHistory)): ?>
            <div class="bg-gray-50 p-6 rounded-lg text-center">
                <img src="https://images.unsplash.com/photo-1631549916768-4119b4123a21?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1179&q=80"
                     alt="No medications" class="w-32 h-32 object-cover rounded-full mx-auto mb-4">
                <p class="text-gray-500 italic">No recent medications.</p>
            </div>
        <?php else: ?>
            <div class="space-y-4">
                <?php foreach ($recentMedicalHistory as $medication): ?>
                    <div class="bg-primary bg-opacity-5 rounded-lg p-4 border-l-4 border-primary">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-capsules text-primary mr-2"></i>
                            <div class="font-semibold"><?= htmlspecialchars($medication['medication_name']) ?></div>
                        </div>
                        <div class="flex items-center mb-2">
                            <i class="fas fa-prescription text-primary mr-2"></i>
                            <div class="text-sm text-gray-600">
                                <?= htmlspecialchars($medication['dosage']) ?> -
                                <?= htmlspecialchars($medication['frequency']) ?>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-calendar-check text-primary mr-2"></i>
                            <div class="text-sm text-gray-600">
                                Prescribed: <?= date('M j, Y', strtotime($medication['prescribed_date'])) ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="mt-6 text-center">
                <a href="medical_history.php" class="inline-flex items-center text-primary hover:text-primary-dark transition">
                    View full medical history <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Health Stats -->
<div class="bg-white rounded-lg shadow-md p-8 mt-6 slide-in-up" style="animation-delay: 0.4s;">
    <div class="flex items-center mb-6">
        <div class="bg-gradient-to-r from-primary to-secondary p-3 rounded-full mr-4 text-white">
            <i class="fas fa-heartbeat text-2xl"></i>
        </div>
        <h2 class="text-2xl font-semibold text-gray-800">Your Health Information</h2>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Personal Information -->
        <div class="bg-gray-50 p-6 rounded-lg">
            <div class="flex items-center mb-4">
                <div class="bg-primary bg-opacity-10 p-2 rounded-full mr-3">
                    <i class="fas fa-user text-primary"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-700">Personal Information</h3>
            </div>

            <div class="space-y-4">
                <div class="flex items-start">
                    <div class="text-primary mr-3 mt-1 w-6 text-center">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500">Full Name</div>
                        <div class="font-medium"><?= htmlspecialchars($patient['full_name']) ?></div>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="text-primary mr-3 mt-1 w-6 text-center">
                        <i class="fas fa-venus-mars"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500">Gender</div>
                        <div class="font-medium"><?= htmlspecialchars($patient['gender']) ?></div>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="text-primary mr-3 mt-1 w-6 text-center">
                        <i class="fas fa-birthday-cake"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500">Date of Birth</div>
                        <div class="font-medium"><?= date('F j, Y', strtotime($patient['date_of_birth'])) ?></div>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="text-primary mr-3 mt-1 w-6 text-center">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500">Phone</div>
                        <div class="font-medium"><?= htmlspecialchars($patient['phone']) ?></div>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="text-primary mr-3 mt-1 w-6 text-center">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500">Address</div>
                        <div class="font-medium"><?= htmlspecialchars($patient['address']) ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Health Metrics -->
        <div class="bg-gray-50 p-6 rounded-lg">
            <div class="flex items-center mb-4">
                <div class="bg-secondary bg-opacity-10 p-2 rounded-full mr-3">
                    <i class="fas fa-chart-line text-secondary"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-700">Recent Health Metrics</h3>
            </div>

            <div class="text-center py-8">
                <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80"
                     alt="Health metrics" class="w-32 h-32 object-cover rounded-full mx-auto mb-4">
                <p class="text-gray-500 italic mb-2">No recent health metrics available.</p>
                <p class="text-sm text-gray-600">Health metrics will be updated after your next appointment.</p>

                <div class="mt-6">
                    <a href="book_appointment.php" class="inline-flex items-center px-4 py-2 bg-secondary text-white rounded-lg hover:bg-secondary-dark transition">
                        <i class="fas fa-calendar-plus mr-2"></i> Schedule a check-up
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
