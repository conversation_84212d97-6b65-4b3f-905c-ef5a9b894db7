<?php
/**
 * User Creation Form
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Require admin login
requireLogin('../auth/admin_login.php');
requireRole('admin', '../auth/admin_login.php');

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $full_name = $_POST['full_name'] ?? '';
    $role = $_POST['role'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $department = $_POST['department'] ?? '';
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // Validation
    $errors = [];
    if (empty($username)) $errors[] = "Username is required";
    if (empty($email)) $errors[] = "Email is required";
    if (empty($password)) $errors[] = "Password is required";
    if (empty($confirm_password)) $errors[] = "Confirm password is required";
    if (empty($full_name)) $errors[] = "Full name is required";
    if (empty($role)) $errors[] = "Role is required";
    
    // Password validation
    if ($password !== $confirm_password) {
        $errors[] = "Passwords do not match";
    }
    if (strlen($password) < 6) {
        $errors[] = "Password must be at least 6 characters long";
    }
    
    // Email validation
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format";
    }
    
    if (empty($errors)) {
        try {
            // Check if username or email already exists
            $existing_user = fetchOne(
                "SELECT id FROM users WHERE username = ? OR email = ?",
                "ss",
                [$username, $email]
            );
            
            if ($existing_user) {
                $errors[] = "Username or email already exists";
            } else {
                // Hash password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // Insert user record
                $sql = "INSERT INTO users (username, email, password, full_name, role, phone, department, is_active, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                $result = executeQuery($sql, "sssssssi", [
                    $username, $email, $hashed_password, $full_name, $role, $phone, $department, $is_active
                ]);
                
                if ($result) {
                    $_SESSION['flash_message'] = "User created successfully!";
                    $_SESSION['flash_type'] = "success";
                    header("Location: user_creation.php");
                    exit;
                } else {
                    $errors[] = "Failed to create user. Please try again.";
                }
            }
        } catch (Exception $e) {
            $errors[] = "Database error: " . $e->getMessage();
        }
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-cyan-600 to-blue-600 text-white rounded-xl shadow-lg p-8 mb-8">
        <div class="flex items-center">
            <i class="fas fa-users-cog text-4xl mr-4"></i>
            <div>
                <h1 class="text-3xl font-bold">User Creation</h1>
                <p class="text-cyan-100 mt-1">Create new user accounts for hospital staff</p>
            </div>
        </div>
    </div>

    <!-- Display Errors -->
    <?php if (!empty($errors)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <ul class="list-disc list-inside">
                <?php foreach ($errors as $error): ?>
                    <li><?= htmlspecialchars($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- User Creation Form -->
    <div class="bg-white rounded-xl shadow-lg p-8">
        <form method="POST" class="space-y-6">
            <!-- Account Information -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-user-circle text-blue-600 mr-2"></i>
                    Account Information
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Username *</label>
                        <input type="text" name="username" required 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Enter username"
                               value="<?= htmlspecialchars($_POST['username'] ?? '') ?>">
                        <p class="text-sm text-gray-500 mt-1">Username must be unique</p>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Email Address *</label>
                        <input type="email" name="email" required 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="<EMAIL>"
                               value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Password *</label>
                        <input type="password" name="password" required 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Enter password"
                               minlength="6">
                        <p class="text-sm text-gray-500 mt-1">Minimum 6 characters</p>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Confirm Password *</label>
                        <input type="password" name="confirm_password" required 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Confirm password"
                               minlength="6">
                    </div>
                </div>
            </div>

            <!-- Personal Information -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-id-card text-green-600 mr-2"></i>
                    Personal Information
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Full Name *</label>
                        <input type="text" name="full_name" required 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="Enter full name"
                               value="<?= htmlspecialchars($_POST['full_name'] ?? '') ?>">
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Phone Number</label>
                        <input type="tel" name="phone" 
                               class="form-input border-gray-300 focus:border-blue-500" 
                               placeholder="+234 xxx xxx xxxx"
                               value="<?= htmlspecialchars($_POST['phone'] ?? '') ?>">
                    </div>
                </div>
            </div>

            <!-- Role and Permissions -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-shield-alt text-purple-600 mr-2"></i>
                    Role & Permissions
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label text-gray-700">Role *</label>
                        <select name="role" required class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Role</option>
                            <option value="admin" <?= ($_POST['role'] ?? '') === 'admin' ? 'selected' : '' ?>>Administrator</option>
                            <option value="doctor" <?= ($_POST['role'] ?? '') === 'doctor' ? 'selected' : '' ?>>Doctor</option>
                            <option value="nurse" <?= ($_POST['role'] ?? '') === 'nurse' ? 'selected' : '' ?>>Nurse</option>
                            <option value="pharmacist" <?= ($_POST['role'] ?? '') === 'pharmacist' ? 'selected' : '' ?>>Pharmacist</option>
                            <option value="technician" <?= ($_POST['role'] ?? '') === 'technician' ? 'selected' : '' ?>>Technician</option>
                            <option value="receptionist" <?= ($_POST['role'] ?? '') === 'receptionist' ? 'selected' : '' ?>>Receptionist</option>
                            <option value="patient" <?= ($_POST['role'] ?? '') === 'patient' ? 'selected' : '' ?>>Patient</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label text-gray-700">Department</label>
                        <select name="department" class="form-input border-gray-300 focus:border-blue-500">
                            <option value="">Select Department</option>
                            <option value="administration" <?= ($_POST['department'] ?? '') === 'administration' ? 'selected' : '' ?>>Administration</option>
                            <option value="emergency" <?= ($_POST['department'] ?? '') === 'emergency' ? 'selected' : '' ?>>Emergency</option>
                            <option value="cardiology" <?= ($_POST['department'] ?? '') === 'cardiology' ? 'selected' : '' ?>>Cardiology</option>
                            <option value="neurology" <?= ($_POST['department'] ?? '') === 'neurology' ? 'selected' : '' ?>>Neurology</option>
                            <option value="orthopedics" <?= ($_POST['department'] ?? '') === 'orthopedics' ? 'selected' : '' ?>>Orthopedics</option>
                            <option value="pediatrics" <?= ($_POST['department'] ?? '') === 'pediatrics' ? 'selected' : '' ?>>Pediatrics</option>
                            <option value="surgery" <?= ($_POST['department'] ?? '') === 'surgery' ? 'selected' : '' ?>>Surgery</option>
                            <option value="radiology" <?= ($_POST['department'] ?? '') === 'radiology' ? 'selected' : '' ?>>Radiology</option>
                            <option value="laboratory" <?= ($_POST['department'] ?? '') === 'laboratory' ? 'selected' : '' ?>>Laboratory</option>
                            <option value="pharmacy" <?= ($_POST['department'] ?? '') === 'pharmacy' ? 'selected' : '' ?>>Pharmacy</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Account Status -->
            <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-toggle-on text-orange-600 mr-2"></i>
                    Account Status
                </h3>
                <div class="flex items-center">
                    <input type="checkbox" name="is_active" id="is_active" 
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                           <?= ($_POST['is_active'] ?? '1') ? 'checked' : '' ?>>
                    <label for="is_active" class="ml-2 text-sm font-medium text-gray-700">
                        Account is active (user can log in)
                    </label>
                </div>
                <p class="text-sm text-gray-500 mt-2">Uncheck this to create an inactive account that cannot be used to log in</p>
            </div>

            <!-- Role Descriptions -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Role Descriptions</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                        <strong class="text-blue-600">Administrator:</strong> Full system access, can manage all users and settings
                    </div>
                    <div>
                        <strong class="text-green-600">Doctor:</strong> Can view/edit patient records, manage appointments
                    </div>
                    <div>
                        <strong class="text-purple-600">Nurse:</strong> Can view patient records, update care notes
                    </div>
                    <div>
                        <strong class="text-orange-600">Pharmacist:</strong> Can manage medications and prescriptions
                    </div>
                    <div>
                        <strong class="text-teal-600">Technician:</strong> Can access lab/radiology systems
                    </div>
                    <div>
                        <strong class="text-pink-600">Receptionist:</strong> Can manage appointments and basic patient info
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="window.history.back()" 
                        class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
                    Cancel
                </button>
                <button type="submit" 
                        class="btn-primary px-8 py-3 bg-cyan-600 hover:bg-cyan-700 text-white rounded-lg transition">
                    <i class="fas fa-user-plus mr-2"></i>
                    Create User
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Password confirmation validation
document.addEventListener('DOMContentLoaded', function() {
    const password = document.querySelector('input[name="password"]');
    const confirmPassword = document.querySelector('input[name="confirm_password"]');
    
    function validatePassword() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity("Passwords don't match");
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    password.addEventListener('change', validatePassword);
    confirmPassword.addEventListener('keyup', validatePassword);
});
</script>

<?php include_once '../includes/footer.php'; ?>
