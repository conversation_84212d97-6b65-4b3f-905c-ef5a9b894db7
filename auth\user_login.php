<?php
/**
 * User/Patient Login Page
 */

// Include necessary files
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Redirect if already logged in
if (isLoggedIn() && hasRole('patient')) {
    header("Location: ../user/dashboard.php");
    exit;
}

// Process login form
$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error = "Please enter both username and password";
    } else {
        // Attempt login
        $result = loginUser($username, $password, 'patient');
        
        if ($result === true) {
            // Set success message
            $_SESSION['flash_message'] = "Login successful!";
            $_SESSION['flash_type'] = "success";
            
            // Redirect to dashboard
            header("Location: ../user/dashboard.php");
            exit;
        } else {
            $error = $result;
        }
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden mt-10">
    <div class="bg-blue-600 text-white py-4 px-6">
        <h2 class="text-xl font-semibold">Patient Login</h2>
    </div>
    
    <form method="POST" action="" class="py-6 px-8">
        <?php if (!empty($error)): ?>
            <div class="mb-4 p-3 bg-red-100 text-red-800 rounded">
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <div class="mb-4">
            <label for="username" class="block text-gray-700 font-medium mb-2">Username</label>
            <input type="text" id="username" name="username" value="<?= htmlspecialchars($username ?? '') ?>" 
                   class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                   required>
        </div>
        
        <div class="mb-6">
            <label for="password" class="block text-gray-700 font-medium mb-2">Password</label>
            <input type="password" id="password" name="password" 
                   class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                   required>
        </div>
        
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <input type="checkbox" id="remember" name="remember" class="h-4 w-4 text-blue-600">
                <label for="remember" class="ml-2 text-gray-700">Remember me</label>
            </div>
            <a href="#" class="text-blue-600 hover:underline">Forgot password?</a>
        </div>
        
        <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition">
            Login
        </button>
        
        <div class="mt-4 text-center">
            <p class="text-gray-600">Don't have an account? <a href="user_register.php" class="text-blue-600 hover:underline">Register here</a></p>
        </div>
    </form>
</div>

<?php include_once '../includes/footer.php'; ?>
