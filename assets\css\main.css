/* Main CSS file for UBTH Hospital Management System */

/* Basic styles */
body {
  background-color: #f0f8ff !important;
  font-family: 'Poppins', sans-serif;
}

/* Header */
header {
  background: linear-gradient(135deg, #00a86b, #0077cc) !important;
  color: white;
}

/* Hero section */
.hero-section {
  background-image: url('https://images.unsplash.com/photo-1519494026892-80bbd2d6fd0d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1253&q=80');
  background-size: cover;
  background-position: center;
  position: relative;
  color: white;
  padding: 100px 0;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 168, 107, 0.7), rgba(0, 119, 204, 0.7));
  z-index: 1;
}

.hero-section > div {
  position: relative;
  z-index: 2;
}

/* Footer */
footer {
  background: linear-gradient(135deg, #005ea3, #008c58) !important;
  color: white;
}

/* Buttons */
.btn-primary {
  background-color: #00a86b !important;
  border-color: #00a86b !important;
}

.btn-primary:hover {
  background-color: #008c58 !important;
  border-color: #008c58 !important;
}

.btn-secondary {
  background-color: #0077cc !important;
  border-color: #0077cc !important;
}

.btn-secondary:hover {
  background-color: #005ea3 !important;
  border-color: #005ea3 !important;
}

/* Service cards */
.service-card {
  transition: all 0.3s ease;
  border-left: 4px solid #00a86b;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  border-left-color: #0077cc;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-in-up {
  animation: slideInUp 0.5s ease-out;
}
